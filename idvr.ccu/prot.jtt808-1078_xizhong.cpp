#include "prot.jtt808-1078_xizhong.h"
#include "json.hpp"
#include <fstream>

#define EXT_CAN_ID_PATH     "/data/minieye/idvr/etc/ext_can_id.json"

using Json = nlohmann::json;

static std::vector<uint32_t> gCanFilterList = {
    0x18feef00, 0x0cf00400, 0x18feee00, 0x18fe5600,
    0x18fef200, 0x18fee900, 0x18fef500, 0x18fef100,
    0x18fee000, 0x18f00503, 0x18fef700, 0x0cf00300,
    0x18FEFC17, 0x18FF32E5, 0x18FECA00, 0x18FECB00,
    0x18FEDF00, 0x18FEE500, 0x18FEF121, 0x18FEEC00,
    0x18FF0800, 0x18FD9BA3, 0x18FEF527, 0x18F00E51,
    0x18F00F52, 0x18FDB400, 0x18FE56A3, 0x0CF00203,
    0x18FEEB00,
};

static std::vector<uint32_t> gCanSndList = {
    0x18ea0017, 0x18ea0021, 0x18ea00ee, 0x18ea0017,
};

JttTcpClient_XiZhong::JttTcpClient_XiZhong(const char * strSim, const char * tag)
    :
    JttTcpClient(strSim, tag)
{
    if (access("/data/req_frame_snd", F_OK) == 0) {
        //mFrameSnd = true;
    }
    if (access("/data/dump_upload_can", F_OK) == F_OK) {
        mbIsDumpCanId = true;
    }
    mLastSndTs = my::timestamp::now();
    mLastRecvTs = my::timestamp::now();
    mBindSimFailSpchTm = my::timestamp();
    initExtCanId();
    initCanFilter();

    char simstat[PROP_VALUE_MAX] = {0};
    __system_property_get(PROP_PERSIST_XIZHONG_BIND_STAT, simstat);
    mbBindSimStat = !!atoi(simstat);

    mLogger = new FileLog("/data/minieye/idvr/mlog/xizhongLog/", 5 * 1024 * 1024, 10);
    mLogger->prepare();
}

bool JttTcpClient_XiZhong::initExtCanId(void)
{
    std::ifstream in(EXT_CAN_ID_PATH);
    try {
        json j;
        in >> j;
        std::vector<std::string> extCanId = j.get<std::vector<std::string>>();
        for (const auto & id : extCanId) {
            logd("%s", id.c_str());
            mExtCanId.push_back(std::strtol(id.c_str(), nullptr, 16));
        }
        logd("ExtCanId size = %d", mExtCanId.size());
    } catch (Json::exception& e) {
        loge("Json:: %s", e.what());
        return false;
    }
    return true;
}

bool JttTcpClient_XiZhong::initCanFilter(void)
{
    Manager & m = Manager::getInstance();
    std::vector<std::pair<uint32_t, uint32_t>> code;
    code.push_back(std::pair<uint32_t, uint32_t>(0xFFF00000, 0x18F00000));
    m.setCanFilterMaskMode(MCU_CAN_IDX_CAN0_SPEED_RES1, false, code);
    code.clear();
    code.push_back(std::pair<uint32_t, uint32_t>(0xFFF00000, 0x0CF00000));
    m.setCanFilterMaskMode(MCU_CAN_IDX_CAN0_SPEED_RES2, false, code);
    return true;
}

bool JttTcpClient_XiZhong::ext_run_ahead()
{
    if (mLastSndTs.elapsed() > 30 * 1000 && (!access("/data/req_frame_snd", F_OK))) {
        mLastSndTs = my::timestamp::now();
        Manager & m = Manager::getInstance();

        // 0x18ea0017
        uint8_t data[8]  = {0xe9, 0xfe, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00};
        m.sendCan0Msg(0x18ea0017, data);

        // 0x18ea0021
        m.sendCan0Msg(0x18ea0021, data);

        // 0x18ea00ee
        m.sendCan0Msg(0x18ea00ee, data);

        // 0x18ea0017
        data[0] = 0xe5;
        m.sendCan0Msg(0x18ea0017, data);
        logd("snd req frame ok");
    }

    bool canStatus = true;
    if (mLastRecvTs.elapsed() > 3 * 1000) {
        canStatus = false;
    }

    // CAN断开到链接
    if (canStatus && !mLastCanStat) {
        mLastCanStat = canStatus;
        setOsdCanStatus(true);
    } 

    // CAN链接到断开
    else if (!canStatus && mLastCanStat) {
        mLastCanStat = canStatus;
        setOsdCanStatus(false);
    }

    // 处理sim锁卡
    checkSimBindStat(false);
    return true;
}

int JttTcpClient_XiZhong::extern_canDataDeal(int canIdx, CanData & canData)
{
    if (!canIdx) {
        mLastRecvTs = my::timestamp::now();
    }
    canData.canID &= ~(1 << 30);
    if (mbIsDumpCanId) {
        //logd("CAN: %#x", canData.canID);
        auto get_uint8_value = [=](int32_t bits) {
            return static_cast<uint8_t>((static_cast<uint64_t>(canData.canData) >> bits) & 0xFF);
        };
        mLogger->mlog("CANID: %#X %02X %02X %02X %02X %02X %02X %02X %02X", canData.canID, 
            get_uint8_value(0), get_uint8_value(8), get_uint8_value(16), get_uint8_value(24),
            get_uint8_value(32), get_uint8_value(40), get_uint8_value(48), get_uint8_value(56));
    }
    return (find(gCanFilterList.begin(), gCanFilterList.end(), canData.canID) == gCanFilterList.end()\
        && (find(mExtCanId.begin(), mExtCanId.end(), canData.canID) == mExtCanId.end()));
}

void JttTcpClient_XiZhong::setOsdCanStatus(bool status)
{
    std::string cmd = "cmd setUiTips ";
    cmd += status ? "CAN已接通 " : "CAN未接通 ";
    cmd += "16777215";
    if (!LogCallProxyCmd::sendReq("media", cmd.c_str())) {
        loge("sock cmd failed %s", cmd.c_str());
    }
}

bool JttTcpClient_XiZhong::onMsgRcved(const std :: shared_ptr < minieye :: AMessage > & msg)
{
    if (msg->what() != EVT_TYPE_PROT_DB_CHECK_RPT_DATA &&
        msg->what() != EVT_TYPE_PROT_DB_CHECK_AREA_ALARM) {
        logd("msg->what 0x%x", msg->what());
    }

    switch (msg->what()) {
        case EVT_TYPE_PROT_HENAN_TELECOM_BIND_SIM: {
            Manager &m = Manager::getInstance();
            Current st = ServiceHelper::getInstance().getStatus();
            int32_t stat = 0;
            msg->findInt32("status", &stat);
            mbBindSimStat = !!stat; // 0 unlock 1 lock;
            logd("status %d simStat %d", stat, mbBindSimStat);
            char status[PROP_VALUE_MAX] = {0};
            sprintf(status, "%d", mbBindSimStat);
            m.propertySet(PROP_PERSIST_XIZHONG_BIND_STAT, status);

            if (mbBindSimStat) {
                char bImei[PROP_VALUE_MAX] = {0};
                char bIccid[PROP_VALUE_MAX] = {0};
                my::string imei, iccid;
                __system_property_get(PROP_PERSIST_XIZHONG_BIND_IMEI, bImei);
                __system_property_get(PROP_PERSIST_XIZHONG_BIND_ICCID, bIccid);
                if (getSimInfo(imei, iccid)) {
                    logd("imei %s, %s", imei.c_str(), bImei);
                    logd("iccid %s, %s", iccid.c_str(), bIccid);
                    if (imei.length() && imei != (const char *)bImei) {
                        m.propertySet(PROP_PERSIST_XIZHONG_BIND_IMEI, imei.c_str());
                    }
                    if (iccid.length() && iccid != (const char *)bIccid) {
                        m.propertySet(PROP_PERSIST_XIZHONG_BIND_ICCID, iccid.c_str());
                    }
                } else {
                    loge("get sim info fail!!!");
                }
            } else {
                checkSimBindStat(true);
            }
            break;
        }

        default: {
            break;
        }
    }

    return true;
}

void JttTcpClient_XiZhong::checkSimBindStat(bool force)
{
    int timeout = mbBindSimStat ? 10 * 1000 : 5 * 60 * 1000;
    if ((force || (mBindSimFailSpchTm.elapsed() > timeout))) {
        mBindSimFailSpchTm = my::timestamp::now();
        bool simChged = false;

        if (mbBindSimStat) {
            char bImei[PROP_VALUE_MAX] = {0};
            char bIccid[PROP_VALUE_MAX] = {0};
            my::string imei, iccid;
            __system_property_get(PROP_PERSIST_XIZHONG_BIND_IMEI, bImei);
            __system_property_get(PROP_PERSIST_XIZHONG_BIND_ICCID, bIccid);
            if (getSimInfo(imei, iccid)) {
                if (imei.length() && iccid.length() && bIccid[0] && bImei[0] &&
                    (iccid != (const char *)bIccid)) {
                    simChged = true;
                }
            } else {
                loge("get sim info fail!!!");
            }
        }

        Manager & m = Manager::getInstance();
        ComController::Ptr cc = m.com_controller;
        for (int i = 0; i < MAX_CLIENTS; i++) {
            ComService::Ptr cs = (*cc)[i];
            if (cs && simChged) {
                cs->dummy(true);
                //TTS_AUDIO_PLAY("4G通信卡不匹配,已关闭平台连接", false);
                logd("4G Sim Not Mark!!! disconnect all server!!!");
            } else if (cs && !simChged) {
                cs->dummy(simChged);
            }
        }
    }
}

