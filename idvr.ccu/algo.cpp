#undef LOG_TAG_STR
#define LOG_TAG_STR algo

#include <sys/system_properties.h>
#include <sys/prctl.h>
#include "json.hpp"
#include "algo.h"
#include "manager.h"
#include <fstream>
#include "opencv2/imgcodecs.hpp"
#include "opencv2/imgproc.hpp"
#include <msgpack.h>
#include <msgpack.hpp>

#define STOP_LINE  1/*停止线*/
#define ZEBRA_LINE 2/*斑马线*/

struct RoadMarkHub {
    int id;                                             // 路面标识id
    std::vector<std::pair<float, float>> corner_ptrs;   // 图像坐标
    std::vector<std::pair<float, float>> w_corner_pts;  // 世界坐标
    int type;                                           // 路面标识类型
    float dist; // 距离斑马线前多少米
    MSGPACK_DEFINE_MAP(corner_ptrs, w_corner_pts, type, dist, id);
    
    bool validWidth(float thres)
    {
        float width = 0;
        if (w_corner_pts.size()) {
            float min = w_corner_pts[0].second;
            float max = w_corner_pts[0].second;
            for (auto i : w_corner_pts) {
                min = std::min<float>(min, i.second);
                max = std::max<float>(max, i.second);
            }
            width = (max - min);
            //logd("width %f = %f - %f", width, max, min);
            return (width > thres);
        }
        return false;
    }

    bool Judge(int width) {
        float min_x = 999.0;
        float max_x = -999.0;
        float min_y = 999.0;
        float max_y = -999.0;
        for (size_t i = 0; i < w_corner_pts.size(); i++) {
            min_x = std::min(min_x, w_corner_pts[i].first);
            min_y = std::min(min_y, w_corner_pts[i].second);
    
            max_x = std::max(max_x, w_corner_pts[i].first);
            max_y = std::max(max_y, w_corner_pts[i].second);
        }

        if (max_y < -width || min_y > width) {
            return false;
        }
        return true;
    }
};

using json = nlohmann::json;
SINGLETON_STATIC_INSTANCE(AlgoManager);
struct MediaParam {
    my::string color;   // 算法需要的图像颜色数据格式
    int width;          // 算法需要的图像参数
    int height;         // 算法需要的图像参数
    int fps;            // 给算法喂图的帧率
    int msgPort;

    MediaParam(const char * c, int w, int h, int f, int32_t port = 0)
    {
        color = c;
        width = w;
        height = h;
        fps = f;
        msgPort = port;
    }
};
class LibflowRcver
    : public flow::Receiver
    , public my::thread
{
    public:
        virtual void recv(const char* source,  // '\0' terminated string
                          const char* topic,   // any binary data
                          const char* data,    // any binary data
                              size_t size);    // < 2^32
        bool start();
    private:
        void run();
    private:
        std::mutex mMtx;
        bool mbStart = false;
        int mSnapSeq = 0;
        std::map<int, std::pair<std::string, my::timestamp>> mSnapTask;
};
// 算法库基类
class Algo
{
    public:
        Algo() {}
        virtual ~Algo() {}

        virtual int start()
        {
            return -1;
        }
        virtual void stop()  { };

        virtual int sendMsg(const std :: string & topic, const char * data, size_t size) = 0;
};

class AlgoProt : public Algo, public ILibFlowCallback
{
    public:
        AlgoProt(const char * type, const char * server, const char * port, const char * topic, struct MediaParam & param)
            : mAlgoName(type)
            , mServer(server)
            , mPort(port)
            , mTopic(topic)
            , mParam(param)
            , mLibflowClient(NULL)
            , started(false)
        {
            if (param.msgPort > 0) {
                mPort = std::to_string(param.msgPort);
            }
        }

        ~AlgoProt()
        {
            stop();
        }

        // 启动算法
        int start()
        {
            std::lock_guard<std::mutex> lock(m_mutex);

            if (started) {
                logw("[AlgoProt::start] already started, %s:%s\n", mServer.c_str(), mPort.c_str());
                return 0;
            }

            // 1、libflow初始化
            mLibflowClient = new LibflowClient(mServer.c_str(), mPort.c_str(), mTopic.c_str());

            if (mLibflowClient == NULL || mLibflowClient->start(this) != 0) {
                loge("[AlgoProt::start] start fail!, %s:%s\n", mServer.c_str(), mPort.c_str());
                return -1;
            }

            started = true;
            return 0;
        }

        // 停止算法
        void stop()
        {
            std::lock_guard<std::mutex> lock(m_mutex);

            if (started == false) {
                logw("[AlgoProt::start] already stoped, %s:%s\n", mServer.c_str(), mPort.c_str());
                return;
            }

            // 释放libflow client
            if (mLibflowClient) {
                mLibflowClient->stop();
                free(mLibflowClient);
                mLibflowClient = NULL;
            }

            started = false;

        }
        virtual const char * AlgoName()
        {
            return mAlgoName.c_str();
        }
        virtual int sendMsg(const std :: string & topic, const char * data, size_t size)
        {
            return mLibflowClient->send(topic, data, size);
        }

        void onAudioMsg(const char * data, size_t size)
        {
            logi("algo %s json : %s\n", AlgoName(), data);
            std::string recv_str(data, size);

            try {
                //以json格式解析协议
                json recv_json = json::parse(recv_str);
                int frame_id = recv_json.at(AlgoName()).at("frame_id");
                uint64_t ts  = recv_json.at(AlgoName()).at("time_ms");
                double   spd = recv_json.at(AlgoName()).at("speed");
                //获取语音项目
                std::string typ = recv_json.at(AlgoName()).at("type");
                std::string action = recv_json.at(AlgoName()).at("action");
                std::string path = recv_json.at(AlgoName()).at("path");
                int priority = recv_json.at(AlgoName()).at("priority");

                if (typ == "audio" && action == "play") {
                    //播放语音
                    int  iSortPriority = path2priority(path.c_str(), priority);
                    int ret = cmd2voice(path.c_str(), iSortPriority);
                    logw("onAudioMsg type '%s', action '%s' path '%s' priority '%d' ret = %d\n",
                         typ.c_str(), action.c_str(), path.c_str(), iSortPriority, ret);

                } else {
                    logw("type '%s', action '%s'\n",
                         typ.c_str(), action.c_str());
                }

            } catch (json::exception & e) {
                loge("[algo::exception] %s\n", e.what());
            }
        }
        void onAlgoCanMsg(const char * data, size_t size)
        {
            /*
                - topic: ```minieye.can_info_json.v1```
                - 端口: ```24100```
                ```json
                {
                    "can_id": "0x0CFFBDE1",    # string
                    "can" :  "can1/can2" ,     # string
                    "customer" : "haoyue",     # string
                    "can_data" : "base64_string" # unsigned char[8] base64 encode
                }
            */
            Manager & m = Manager::getInstance();
            logi("algo %s json : %s\n", AlgoName(), data);
            std::string recv_str(data, size);

            try {
                //以json格式解析协议
                json recv_json = json::parse(recv_str);
                std::string canPort = recv_json.at("can");
                std::string canId   = recv_json.at("can_id");
                std::string canData = recv_json.at("can_data");
                std::string customer = recv_json.at("customer");
                logd("%s send [%s] %s = %s", customer.c_str(), canPort.c_str(), canId.c_str(), canData.c_str());

                uint32_t id = strtoul(canId.c_str(), NULL, 16);

                if (!id) {
                    loge("invalid can id %d", id);
                    return ;
                }

                McuMsgCanIdxE canidx = MCU_CAN_IDX_CAN0_SPEED_RES1;

                if (canPort == "can1") {
                    canidx = MCU_CAN_IDX_CAN0_SPEED_RES1;

                } else if (canPort == "can2") {
                    canidx = MCU_CAN_IDX_CAN1_DISP_RES1;
                }

                if (id && (mCanIdData[canidx].find(id) == mCanIdData[canidx].end())) {
                    mCanIdData[canidx][id] = "";

                    if (mCanIdData[canidx].size() > 4) {
                        loge("too many can id! %d", mCanIdData[canidx].size());
                    }

                    McuMsgCanFilterT filter;
                    memset(&filter, 0, sizeof(filter));
                    filter.enable = 1;
                    filter.canIdx = canidx;
                    filter.useListMode = true;
                    int idx = 0;

                    for (auto i : mCanIdData[canidx]) {
                        filter.canIds[idx++] = i.first;

                        if (CAN_ID_ARRAY_SIZE == idx) {
                            break;
                        }
                    }

                    m.sendCanFilterMsg((uint8_t *)&filter, sizeof(McuMsgCanFilterT));
                }

                my::string rawData = my::base64d(my::constr(canData.c_str(), canData.length()));
                mCanIdData[canidx][id] = rawData;
                logd("id 0x%x size %d, [%02x %02x %02x %02x %02x %02x %02x %02x]", id, rawData.length(),
                     (unsigned char)rawData[0], (unsigned char)rawData[1], (unsigned char)rawData[2], (unsigned char)rawData[3],
                     (unsigned char)rawData[4], (unsigned char)rawData[5], (unsigned char)rawData[6], (unsigned char)rawData[7]);

                if (MCU_CAN_IDX_CAN0_SPEED_RES1 == canidx) {
                    m.sendCan0Msg(id, (uint8_t*)rawData.c_str());

                } else if (MCU_CAN_IDX_CAN1_DISP_RES1 == canidx) {
                    m.sendCan1Msg(id, (uint8_t*)rawData.c_str());
                }

            } catch (json::exception & e) {
                loge("[algo::exception] %s\n", e.what());
            }
        }
    public:
        virtual void onAlgoMsg(const char *source,  // '\0' terminated string
                               const char *topic,   // any binary data
                               const char *data,    // any binary data
                               size_t size) = 0;
        // 处理算法报警数据
        void OnLibFlowCallback(const char *source,  // '\0' terminated string
                               const char *topic,   // any binary data
                               const char *data,    // any binary data
                               size_t size)        // < 2^32
        {
            if (!mbSetThdName) {
                mbSetThdName = true;
                prctl(PR_SET_NAME, (mAlgoName + "Flow").c_str());
            }

            if (!strcmp(topic, "adas.audio_info_json.v1") ||
                !strcmp(topic, "dms.audio_info_json.v1")) {  // 处理语音消息
                onAudioMsg(data, size);

            } else if (!strcmp(topic, "minieye.can_info_json.v1")) { // 算法结果can协议包
                onAlgoCanMsg(data, size);

            } else {
                onAlgoMsg(source, topic, data, size);
            }
        }
    protected:
        bool mbSetThdName = false;
        std::mutex m_mutex;
        bool started;
        std::string mAlgoName;
        MediaParam mParam;

        alarmVoiceControlCallbackFun mVoiceControlCallBack = NULL;

        std::string mServer;
        std::string mPort;
        std::string mTopic;
        LibflowClient * mLibflowClient; // for getting algo result&msg

        std::map<uint32_t, my::constr> mCanIdData[MCU_CAN_IDX_CAN_MAX];
};

/*
    https://git.minieye.tech/algo_emb/interface/-/blob/master/cabin/DFH1935/DFH1935_base_protocol.md
*/
class DmsProtBase : public AlgoProt
{
    public:
        DmsProtBase(const char * name, const char * server, const char * port, const char * topic, MediaParam & param)
            : AlgoProt(name, server, port, topic, param)
        {
        }

        ~DmsProtBase()
        {
        }

        // 处理算法报警数据
        virtual void onAlgoMsg(const char *source,  // '\0' terminated string
                               const char *topic,   // any binary data
                               const char *data,    // any binary data
                               size_t size)        // < 2^32
        {
            AlgoManager & am = AlgoManager::getInstance();
            Manager & m = Manager::getInstance();
            //logd("algo %s json : %s\n", AlgoName(), data);
            std::string topicName = mAlgoName + ".alert_info_json.v1";

            if (topicName == topic) {
                std::string recv_str(data, size);

                try {
                    //以json格式解析协议
                    json recv_json = json::parse(recv_str);
                    int frame_id = recv_json.at(AlgoName()).at("frame_id");
                    uint64_t ts  = recv_json.at(AlgoName()).at("time_ms");
                    double   spd = recv_json.at(AlgoName()).at("speed");
                    //获取报警项目
                    std::vector<std::string> alerts_list = recv_json.at(AlgoName()).at("alerts");

                    if (alerts_list.empty()) {
                        //logd(" Frame ID '%d' empty\n", frame_id);

                    } else {
                        logd(" Frame ID '%d' get '%d' alerts\n", frame_id, alerts_list.size());

                        for (std::string e : alerts_list) {
                            EVT_TYPE typ = name2value(AlgoName(), e.c_str());
                            //value2voice(typ);
                            logw(" alerts '%s', typ = %d\n", e.c_str(), typ);

                            if (EVT_TYPE_INVALID != typ) {
                                std::shared_ptr<Event> sp = make_shared<Event>(AlgoName(), typ);
                                sp->c.event = e;
                                sp->c.frameId = frame_id;
                                sp->c.ts = ts;
                                sp->c.speed = spd;
                                am.push(sp);

                                if ((EVT_TYPE_DMS_DRIVER_CHG == typ) ||
                                    (EVT_TYPE_DMS_ABSENCE == typ)) {
                                    std::shared_ptr<minieye::AMessage> amsg = std::make_shared<minieye::AMessage>();
                                    amsg->setWhat(typ);
                                    m.postMsg(amsg);
                                }

                                //记录疲劳报警次数保存到prop
                                if ((EVT_TYPE_DMS_FATIGUE_Eye == typ) ||
                                    (EVT_TYPE_DMS_FATIGUE_YAWN == typ)) {
                                    char temp[10] = {0};
                                    static int fatigue_alarm_count = 0;
                                    fatigue_alarm_count++;
                                    sprintf(temp, "%d", fatigue_alarm_count);
#if 0                                    
                                    __system_property_set(PROP_ALGO_FATIGUE_COUNT, temp);
#else
                                    __system_property_set(PROP_RW_MINIEYE_FATIGUE_COUNT, temp);                    
#endif
                                }
                            }
                        }
                    }

#if 0
                    auto face_id = recv_json.at(AlgoName()).at("face_id");

                    if (face_id.is_object()) {
                        bool snapshot = face_id.at("face_active_capture");

                        if (snapshot) {
                            EVT_TYPE typ = EVT_TYPE_DMS_SNAP;
                            std::shared_ptr<Event> sp = make_shared<Event>(AlgoName(), typ);
                            sp->c.event = "face_active_capture";
                            sp->c.frameId = frame_id;
                            sp->c.ts = ts;
                            sp->c.speed = spd;
                            logd("face_id  snapshot");
                            am.push(sp);
                        }

                        std::string match = face_id.at("id_match_status");

                        do {
                            EVT_TYPE typ = EVT_TYPE_INVALID;

                            if (match == "match") {
                                typ = EVT_TYPE_DMS_DRIVER_MATCH;

                            } else if (match == "not_match") {
                                typ = EVT_TYPE_DMS_DRIVER_NOT_MATCH;

                            } else {
                                break;
                            }

                            std::shared_ptr<Event> sp = make_shared<Event>(AlgoName(), typ);
                            sp->c.event = "face_active_capture";
                            sp->c.frameId = frame_id;
                            sp->c.ts = ts;
                            sp->c.speed = spd;
                            logd("faceIdentify  %s", match.c_str());
                            am.push(sp);
                        } while (false);
                    }

#endif

                } catch (json::exception & e) {
                    loge("algo %s json : %s\n", AlgoName(), data);
                    loge("%s\n", e.what());
                }
            }
        }
};

class DmsProt : public DmsProtBase
{
    public:
        DmsProt(MediaParam & param)
            : DmsProtBase("dms", "127.0.0.1", "24011", "*"/*"dms.alert_info_json.v1"*/, param)
        {
        }

        ~DmsProt()
        {
        }
};

class HodProt : public DmsProtBase
{
    public:
        HodProt(MediaParam & param)
            : DmsProtBase("hod", "127.0.0.1", "24024", "*"/*"hod.alert_info_json.v1"*/, param)
        {
        }

        ~HodProt()
        {
        }
};
struct MATCH_RESULT {
    std::string name;
    float similarity;
};
inline void from_json(const json& j, struct MATCH_RESULT & ks)
{
    j.at("name").get_to(ks.name);
    j.at("similarity").get_to(ks.similarity);
}
inline void from_json(const json& j, std::pair<std::string, float> & ks)
{
    j.at("name").get_to(ks.first);
    j.at("similarity").get_to(ks.second);
}

class DmsFaceIDProt : public AlgoProt
{
        friend class AlgoManager;

    private:
        std::mutex mMsgIdMtx;
        uint32_t mMsgId;

        std::mutex mFaceIdMtx;
        std::unordered_map<std::string  /*imgId*/,
            std::pair<uint32_t /*msg id*/, struct FaceIdMatchRes >> mFaceId2wait;
        std::pair<uint32_t/*msg id*/, cv::Mat> mCurDrvFace;

    private:
        uint32_t getMsgId()
        {
            std::lock_guard<std::mutex> lock(mMsgIdMtx);
            char propValue[PROP_VALUE_MAX] = {0};
            snprintf(propValue, sizeof(propValue), "%d", mMsgId + 1);
            __system_property_set(PROP_RW_MINIEYE_MSG_IDX, propValue);
            return mMsgId++;
        }
        void dumpFeatureList()
        {
            for (auto it : mFaceId2wait) {
                logd("msg_id %d, imgId %s, faceFeature %d", it.second.first,
                     it.first.c_str(), it.second.second.faceFeature.size());
            }
        }
        bool faceFeatureSet(uint32_t msg_id, std::vector<uint8_t> & faceFeature, std::string & whichImgId)
        {
            std::lock_guard<std::mutex> lock(mFaceIdMtx);

            for (auto it = mFaceId2wait.begin(); it != mFaceId2wait.end(); it++) {
                if (it->second.first == msg_id) {
                    it->second.second.faceFeature.assign(faceFeature.begin(), faceFeature.end());
                    whichImgId = it->first;
                    logd("msg_id %d, imgId %s, faceFeature %d, %d", it->second.first, it->first.c_str(),
                         faceFeature.size(), it->second.second.faceFeature.size());
                    return true;
                }
            }

            return false;
        }
    protected:
        int faceDetect()
        {
            mCurDrvFace.first = getMsgId();
            json j = {
                {"msg_id", mCurDrvFace.first},
                {"msg", "avatar_extract"}
            };
            std::string msg = j.dump();
            logd("send json : %s", msg.c_str());

            if (msg.length() == sendMsg("dms.avatar_extractor_json.v1",
                                        msg.c_str(), msg.length())) {
                mCurDrvFace.second.release();
                return 0;
            }

            return -1;
        }
        int faceDetectedGet(const std::string & filePath)
        {
            size_t imgSize = mCurDrvFace.second.step[0] * mCurDrvFace.second.rows;

            if (imgSize > 0) {
                return cv::imwrite(filePath, mCurDrvFace.second);
            }

            return 0;
        }
        int faceFeatureReq(std::string & imgId, std::string & imgPath)
        {
            const char * imgBase64;
            cv::Mat imgMat = cv::imread(imgPath);

            if (!imgMat.data || imgMat.empty()) {
                logd("imread %s failed", imgPath.c_str());
                return -1;
            }

            const char * ext = strrchr(imgPath.c_str(), '.');

            if (!ext) {
                ext = ".jpg";
            }

            std::vector<uint8_t> imgEnc;
            int ret = cv::imencode(ext, imgMat, imgEnc);
            uint32_t msg_id = getMsgId();

            try {
                json j = {
                    {"msg_id", msg_id},
                    {
                        "data", {
                            {"img_width", imgMat.cols},
                            {"img_height", imgMat.rows},
                            {"img_data", imgEnc}
                        }
                    }
                };
                std::string msg = j.dump();

                //logd("send json : %s", msg.c_str());
                if (msg.length() == sendMsg("dms.feature_extractor_json.v1",
                                            msg.c_str(), msg.length())) {
                    logd("sendMsg success! msg_id %d", msg_id);

                    if (mFaceId2wait.find(imgId) == mFaceId2wait.end()) {
                        std::pair<uint32_t /*msg id*/, struct FaceIdMatchRes > pv;
                        pv.first = msg_id;
                        pv.second = FaceIdMatchRes();
                        mFaceId2wait[imgId] = pv;

                    } else {
                        mFaceId2wait[imgId].first = msg_id;
                    }

                    return 0;
                }

            } catch (json::exception & e) {
                loge("%s\n", e.what());
            }

            return -1;
        }
        int faceFeatureGet(std::string & imgId, std::vector<uint8_t> & faceFeature)
        {
            std::lock_guard<std::mutex> lock(mFaceIdMtx);
            auto res = mFaceId2wait.find(imgId);

            if (res != mFaceId2wait.end()) {
                faceFeature = res->second.second.faceFeature;
                logd("%s, faceFeature %d, %d", res->first.c_str(),
                     faceFeature.size(), res->second.second.faceFeature.size());
                return 1;
            }

            return 0;
        }
        int faceFeatureFindMatchReq(std::vector<uint8_t> & curFeature,
                                    std::vector<std::pair<std::string/*imgId*/, std::vector<uint8_t>/*feature*/>> & featureList)
        {
            json j;
            j["msg_id"] = getMsgId();
            json target_features;

            for (auto it : featureList) {
                target_features.push_back({
                    {"name", it.first},
                    {"feature", it.second}
                });
                auto ifw = mFaceId2wait.find(it.first);

                if (ifw == mFaceId2wait.end()) {
                    struct FaceIdMatchRes m;
                    m.faceFeature.assign(it.second.begin(), it.second.end());
                    m.similarity = 0;
                    mFaceId2wait[it.first] = std::pair<uint32_t, struct FaceIdMatchRes >(0, m);
                }
            }

            j["target_features"] = target_features;
            j["current_feature"] = curFeature;

            std::string msg = j.dump();

            //logd("send json : %s", msg.c_str());
            //m.mpLogger->mlog("\n+json size %d\n", msg.length());
            //m.mpLogger->mlog_raw(msg.c_str(), msg.length());
            //m.mpLogger->mlog("\n-json size %d\n", msg.length());
            if (msg.length() == sendMsg("dms.feature_find_match.v1", msg.c_str(), msg.length())) {
                logd("send %s", msg.c_str());
                return 1;
            }

            return 0;
        }
        int faceFeatureFindMatchGet(
            std::unordered_map<std::string  /*imgId*/, struct FaceIdMatchRes > & result)
        {
            std::lock_guard<std::mutex> lock(mFaceIdMtx);
            int count = 0;
            dumpFeatureList();

            for (auto & it : result) {
                auto res = mFaceId2wait.find(it.first);

                if (res != mFaceId2wait.end()) {

                    if (res->second.second.faceFeature.size()) {
                        it.second = res->second.second;
                        count++;
                    }

                    logd("%s : %f, %f", it.first.c_str(), it.second.similarity, res->second.second.similarity);

                } else {
                    loge("%s not found", it.first.c_str());
                }
            }

            return count;
        }
    public:
        DmsFaceIDProt(struct MediaParam & param)
            : AlgoProt("dms", "127.0.0.1", "24015", "*", param)
        {
            char propValue[PROP_VALUE_MAX] = {0};
            memset(propValue, 0, sizeof(propValue));

            if (__system_property_get(PROP_RW_MINIEYE_MSG_IDX, propValue) > 0) {
                mMsgId = atoi(propValue);

            } else {
                mMsgId = 1;
            }
        }

        ~DmsFaceIDProt()
        {
        }

        // 处理算法报警数据
        virtual void onAlgoMsg(const char *source,  // '\0' terminated string
                               const char *topic,   // any binary data
                               const char *data,    // any binary data
                               size_t size)        // < 2^32
        {
            logd("algo %s topic %s json : %s\n", AlgoName(), topic, data);

            if (!strcmp(topic, "dms.avatar_extractor_json.v1")) {
                onFaceDetectResp(data, size);

            } else if (!strcmp(topic, "dms.driver_ready_info_json.v1")) {
                onDriverReadyMsg(data, size);

            } else if (!strcmp(topic, "dms.feature_extractor_json.v1")) {
                onImgFeatureResp(data, size);

            } else if (!strcmp(topic, "dms.feature_find_match.v1")) {
                onFeatureFindMatchResp(data, size);
            }
        }
    private:
        void onFeatureFindMatchResp(const char *data,         size_t size)
        {
            Manager & m = Manager::getInstance();
            std::string recv_str(data, size);

            try {
                //以json格式解析协议
                json recv_json = json::parse(recv_str);
                int msg_id = recv_json.at("msg_id");
                auto result = recv_json.at("match_result");
#if 0
                std::vector<std::pair<std::string, float>> resList = result.get<std::vector<std::pair<std::string, float>>>();

                if (resList.size()) {
                    std::shared_ptr<minieye::AMessage> amsg = std::make_shared<minieye::AMessage>();

                    for (auto it : resList) {
                        auto target = mFaceId2wait.find(it.first);

                        if (target != mFaceId2wait.end()) {
                            target->second.second.similarity = it.second;
                        }
                    }

                    amsg->setWhat(AMSG_WHAT_FACE_MATCH_RESULT);/*get result with faceFeatureFindMatchGet*/
                    m.postMsg(amsg);
                    logd("AMSG_WHAT_FACE_MATCH_RESULT  %d", msg_id);
                }

#else
                std::vector<struct MATCH_RESULT> resList = result.get<std::vector<struct MATCH_RESULT>>();

                if (resList.size()) {
                    std::shared_ptr<minieye::AMessage> amsg = std::make_shared<minieye::AMessage>();

                    for (auto it : resList) {
                        logd("%s : %f", it.name.c_str(), it.similarity);

                        if (it.similarity < 0) {
                            it.similarity = 0;
                        }

                        auto target = mFaceId2wait.find(it.name);

                        if (target != mFaceId2wait.end()) {
                            target->second.second.similarity = it.similarity;
                        }
                    }

                    amsg->setWhat(EVT_TYPE_DMS_FACEID_MATCH_RESULT);/*get result with faceFeatureFindMatchGet*/
                    m.postMsg(amsg);
                    logd("EVT_TYPE_DMS_FACEID_MATCH_RESULT  %d", msg_id);

                } else {
                    loge("no result!!!");
                }

#endif

            } catch (json::exception & e) {
                loge("algo %s json : %s\n", AlgoName(), data);
                loge("%s\n", e.what());
            }
        }
        void  onDriverReadyMsg(const char *data,        size_t size)
        {
            Manager & m = Manager::getInstance();
            std::string recv_str(data, size);

            try {
                //以json格式解析协议
                json recv_json = json::parse(recv_str);
                int msg_id = recv_json.at("msg_id");
                std::string msg = recv_json.at("msg");
                logd("msg %s", msg.c_str());

                if (msg == "driver_ready") {
                    std::shared_ptr<minieye::AMessage> amsg = std::make_shared<minieye::AMessage>();
                    amsg->setWhat(EVT_TYPE_DMS_FACEID_DRIVER_READY);
                    m.postMsg(amsg);
                    logd("driver_ready  %d", msg_id);
                }

            } catch (json::exception & e) {
                loge("algo %s json : %s\n", AlgoName(), data);
                loge("%s\n", e.what());
            }
        }

        void onFaceDetectResp(const char *data,        size_t size)
        {
            Manager & m = Manager::getInstance();
            std::string recv_str(data, size);

            try {
                //以json格式解析协议
                json recv_json = json::parse(recv_str);
                std::string jsonStr = recv_json.dump();
                int msg_id = recv_json.at("msg_id");
                std::string status  = recv_json.at("status");

                if (status == "success") {
                    auto jData = recv_json.at("data");
                    std::string msg  = jData.at("msg");
                    int w = jData.at("avatar_width");
                    int h = jData.at("avatar_height");
                    std::vector<uint8_t> faceImg = jData.at("avatar_data");

                    //m.mpLogger->mlog("\n+recv img %dx%d\n", w, h);
                    //m.mpLogger->mlog_raw((const char *)jsonStr.c_str(), jsonStr.length());
                    //m.mpLogger->mlog("\n-recv img %dx%d\n", w, h);
                    if (1/*mCurDrvFace.first == msg_id*/) {
                        mCurDrvFace.second = cv::imdecode(faceImg, 1);

                    } else {
                        loge("msg id not match! %d != %d", mCurDrvFace.first, msg_id);
                    }

                    size_t imgSize = mCurDrvFace.second.step[0] * mCurDrvFace.second.rows;
                    std::shared_ptr<minieye::AMessage> amsg = std::make_shared<minieye::AMessage>();
                    amsg->setWhat(EVT_TYPE_DMS_FACEID_DETECT_RESP);
                    //amsg->setString("face_img", (const char*)&mCurDrvFace.second, imgSize);
                    //amsg->setInt32("width", mCurDrvFace.second.cols);
                    //amsg->setInt32("height", mCurDrvFace.second.rows);
                    m.postMsg(amsg);

                } else {
                    std::string reason = recv_json.at("reason");
                    loge("reason : %s", reason.c_str());
                }

            } catch (json::exception & e) {
                loge("algo %s json : %s\n", AlgoName(), data);
                loge("%s\n", e.what());
            }
        }

        void onImgFeatureResp(const char *data,       size_t size)
        {
            Manager & m = Manager::getInstance();
            std::string recv_str(data, size);

            try {
                //以json格式解析协议
                json recv_json = json::parse(recv_str);
                int msg_id = recv_json.at("msg_id");
                std::string status  = recv_json.at("status");

                if (status == "success") {
                    auto jData = recv_json.at("data");
                    std::string msg  = jData.at("msg");
                    int faceIdLen = jData.at("face_id_length");
                    std::vector<uint8_t> faceId = jData.at("face_id_data");
                    std::string whichImgId;

                    if (!faceFeatureSet(msg_id, faceId, whichImgId)) {
                        loge("msg id %d, not found!", msg_id);

                    } else {
                        dumpFeatureList();
                        std::shared_ptr<minieye::AMessage> amsg = std::make_shared<minieye::AMessage>();
                        amsg->setWhat(EVT_TYPE_DMS_FACEID_IMG_TO_FACE_ID_RESP);/*faceFeatureGet*/
                        amsg->setString("img_id", whichImgId.c_str(), whichImgId.length());
                        amsg->setString("face_id", (const char *)faceId.data(), faceId.size());
                        m.postMsg(amsg);
                    }

                } else {
                    std::string reason = recv_json.at("reason");
                    loge("reason : %s", reason.c_str());
                }

            } catch (json::exception & e) {
                //loge("algo %s json : %s\n", AlgoName(), data);
                loge("%s\n", e.what());
            }
        }
};


class DmsFaceIDProt_V2 : public AlgoProt
{
        friend class AlgoManager;

    private:
        std::mutex mMsgIdMtx;
        uint32_t mMsgId;

        std::mutex mFaceIdMtx;
        std::unordered_map<std::string  /*imgId*/, std::pair<uint32_t /*msg id*/, struct FaceIdMatchRes >> mFaceId2wait;
        std::pair<uint32_t/*msg id*/, cv::Mat> mCurDrvFace;
    private:
        uint32_t getMsgId()
        {
            std::lock_guard<std::mutex> lock(mMsgIdMtx);
            char propValue[PROP_VALUE_MAX] = {0};
            snprintf(propValue, sizeof(propValue), "%d", mMsgId + 1);
            __system_property_set("rw.minieye.faceV2msg_idx", propValue);

            logd("new msgid:%d!\n", mMsgId + 1);
            return mMsgId++;
        }
        void dumpFeatureList()
        {
            for (auto it : mFaceId2wait) {
                logd("msg_id %d, imgId %s, faceFeature %d", it.second.first,
                     it.first.c_str(), it.second.second.faceFeature.size());
            }
        }
        bool faceFeatureSet(uint32_t msg_id, std::vector<uint8_t> & faceFeature, std::string & whichImgId)
        {
            std::lock_guard<std::mutex> lock(mFaceIdMtx);

            for (auto it = mFaceId2wait.begin(); it != mFaceId2wait.end(); it++) {
                if (it->second.first == msg_id) {
                    it->second.second.faceFeature.assign(faceFeature.begin(), faceFeature.end());
                    whichImgId = it->first;
                    logd("msg_id %d, imgId %s, faceFeature %d, %d", it->second.first, it->first.c_str(),
                         faceFeature.size(), it->second.second.faceFeature.size());
                    return true;
                }
            }

            return false;
        }

    public:
        DmsFaceIDProt_V2(struct MediaParam & param)
            : AlgoProt("aps", "127.0.0.1", "24100", "*", param)
        {
            char propValue[PROP_VALUE_MAX] = {0};
            mLibflowServer = new LibflowServer("127.0.0.1", "24101", "MINIEYE.FaceRegister.v1");

            if (mLibflowServer) {
                logd("DmsFaceIDProt_V2 mLibflowServer new success!\n");
                mLibflowServer->start();

            } else {
                logd("DmsFaceIDProt_V2 mLibflowServer new failed!\n");
            }

            if (__system_property_get("rw.minieye.faceV2msg_idx", propValue) > 0) {
                mMsgId = atoi(propValue);

            } else {
                mMsgId = 1;
            }
        }

        ~DmsFaceIDProt_V2()
        {
        }

        // 处理算法报警数据
        virtual void onAlgoMsg(const char *source,  // '\0' terminated string
                               const char *topic,   // any binary data
                               const char *data,    // any binary data
                               size_t size)        // < 2^32
        {
            if (!strcmp(topic, "aps.distinguish_info_json.v1")) {
                logd("DmsFaceIDProt_V2 topic:%s algo %s json : %s!\n", topic, AlgoName(), data);
                faceMatchGet(data, size);

            } else if (!strcmp(topic, "aps.register_info_json.v1")) {
                /* 注册结果 */
                logd("DmsFaceIDProt_V2 topic:%s algo %s json : %s!\n", topic, AlgoName(), data);
                faceIdRegisterResult(data, size);
            }

            return;
        }

        int faceIdRegisterResult(const char *data, size_t size)
        {
            Manager & m = Manager::getInstance();
            std::string action;
            std::string icCardId;
            std::string img_id;
            std::string status;

            std::string recv_str(data, size);

            try {
                //以json格式解析协议
                json recv_json = json::parse(recv_str);

                /* {"action":"insert","id":"123456789012345678","msg_id":"26","return":"failed"} */
                action = recv_json.at("action");

                if (action == "insert") {
                    status = recv_json.at("return");

                    if (status == "success") {
                        icCardId = recv_json.at("id");
                        img_id = recv_json.at("img_id");
                        EVT_TYPE typ = EVT_TYPE_DMS_FACEID_IMG_TO_FACE_ID_RESP;
                        std::shared_ptr<minieye::AMessage> amsg = std::make_shared<minieye::AMessage>();
                        amsg->setString("img_id", img_id.c_str(), img_id.length());
                        amsg->setString("status", status.c_str(), status.length());
                        amsg->setString("ic_cardId", icCardId.c_str(), icCardId.length());
                        amsg->setWhat(typ);
                        m.postMsg(amsg);

                    } else {
                        img_id = recv_json.at("img_id");
                        EVT_TYPE typ = EVT_TYPE_DMS_FACEID_IMG_TO_FACE_ID_RESP;
                        std::shared_ptr<minieye::AMessage> amsg = std::make_shared<minieye::AMessage>();
                        amsg->setString("img_id", img_id.c_str(), img_id.length());
                        amsg->setString("status", status.c_str(), status.length());
                        amsg->setWhat(typ);
                        m.postMsg(amsg);
                    }

                } else if (action == "update") {
                    status = recv_json.at("return");

                    if (status == "success") {
                        icCardId = recv_json.at("id");
                        img_id = recv_json.at("img_id");
                        EVT_TYPE typ = EVT_TYPE_DMS_FACEID_IMG_TO_FACE_ID_RESP;
                        std::shared_ptr<minieye::AMessage> amsg = std::make_shared<minieye::AMessage>();
                        amsg->setString("img_id", img_id.c_str(), img_id.length());
                        amsg->setString("status", status.c_str(), status.length());
                        amsg->setString("ic_cardId", icCardId.c_str(), icCardId.length());
                        amsg->setWhat(typ);
                        m.postMsg(amsg);

                    } else {
                        img_id = recv_json.at("img_id");
                        EVT_TYPE typ = EVT_TYPE_DMS_FACEID_IMG_TO_FACE_ID_RESP;
                        std::shared_ptr<minieye::AMessage> amsg = std::make_shared<minieye::AMessage>();
                        amsg->setString("img_id", img_id.c_str(), img_id.length());
                        amsg->setString("status", status.c_str(), status.length());
                        amsg->setWhat(typ);
                        m.postMsg(amsg);
                    }

                } else if (action == "delete") {
                    status = recv_json.at("return");
                    logd("delete return %s\n", status.c_str());

                    if (status == "success") {
                        img_id = recv_json.at("img_id");
                        EVT_TYPE typ = EVT_TYPE_DMS_FACEID_V2_DELETE_RESP;
                        std::shared_ptr<minieye::AMessage> amsg = std::make_shared<minieye::AMessage>();
                        amsg->setString("img_id", img_id.c_str(), img_id.length());
                        amsg->setWhat(typ);
                        m.postMsg(amsg);

                    } else {
                        logd("delete failed!\n");
                    }

                } else {
                    logd("faceIdRegisterResult unkown action[%s] \n", action.c_str());
                }

                return 0;

            } catch (json::exception & e) {
                loge("%s\n", e.what());
            }

            return -1;
        }

        int faceMatchGet(const char *data,        size_t size)
        {
            AlgoManager & am = AlgoManager::getInstance();
            std::string driverID;
            std::string driver_name;
            std::string reason;
            std::string status;
            double      spd;
            std::string image_id;
            float similarity;
            std::string faceid_mode;

            std::string recv_str(data, size);

            try {
                //以json格式解析协议
                json recv_json = json::parse(recv_str);
                driverID = recv_json.at("driver_id");
                driver_name = recv_json.at("driver_name");
                spd = recv_json.at("speed");
                status = recv_json.at("status");
                faceid_mode = recv_json.at("faceid_mode");

                logd("status:%s!\n", status.c_str());

                if (status == "success") {
                    similarity = recv_json.at("similarity");
                    image_id = recv_json.at("image_id");
                    std::shared_ptr<Event> sp = make_shared<Event>("dms", EVT_TYPE_DMS_SNAP);
                    sp->c.event = "snapshotDMS";
                    sp->c.frameId = 0;
                    sp->c.ts = my::timestamp::utc_milliseconds();
                    sp->c.speed = spd;
                    AlgoManager::getInstance().push(sp);

                    /* 上报驾驶员识别 */
                    {
                        EVT_TYPE typ = EVT_TYPE_DMS_FACEID_V2_MATCH_SUSSCE;
                        std::shared_ptr<minieye::AMessage> amsg = std::make_shared<minieye::AMessage>();
                        amsg->setString("img_id", image_id.c_str(), image_id.length());
                        amsg->setFloat("similarity", similarity);
                        amsg->setString("faceid_mode", faceid_mode.c_str(), faceid_mode.length());
                        amsg->setWhat(typ);
                        Manager::getInstance().postMsg(amsg);
                    }
                    am.push(sp);
                    logd("\n goto snap!\n");

                } else {
                    reason = recv_json.at("reason");
                    /* 先上报驾驶员身份不符 */
                    {
                        std::shared_ptr<Event> sp = make_shared<Event>("dms", EVT_TYPE_DMS_DRIVER_NOT_MATCH);
                        sp->c.event = "driverNotMatch";
                        sp->c.frameId = 0;
                        sp->c.ts = my::timestamp::utc_milliseconds();
                        sp->c.speed = spd;
                        am.push(sp);
                    }

                    /* 再上报驾驶员身份变更 */
                    {
                        std::shared_ptr<Event> sp1 = make_shared<Event>("dms", EVT_TYPE_DMS_DRIVER_CHG);
                        sp1->c.event = "driverchange";
                        sp1->c.frameId = 0;
                        sp1->c.ts = my::timestamp::utc_milliseconds();
                        sp1->c.speed = spd;
                        am.push(sp1);
                    }

                    /* 上报驾驶员识别 */
                    {
                        EVT_TYPE typ = EVT_TYPE_DMS_FACEID_V2_MATCH_FAIL;
                        std::shared_ptr<minieye::AMessage> amsg = std::make_shared<minieye::AMessage>();
                        amsg->setString("faceid_mode", faceid_mode.c_str(), faceid_mode.length());
                        amsg->setWhat(typ);
                        Manager::getInstance().postMsg(amsg);
                    }
                }

                return 0;

            } catch (json::exception & e) {
                loge("%s\n", e.what());
            }

            return -1;
        }

        int faceRegisterReq(std::string &imgId, std::string &action, std::string &mode, std::string &param)
        {
            const char              *imgBase64;
            std::vector<uint8_t>    imgEnc;
            json j;

            if (action.length() <= 0) {
                logd("input action invalid!\n");
                return -1;

            } else if (imgId.length() <= 0) {
                logd("input imgId invalid!\n");
                return -1;

            } else if (param.length() <= 0) {
                logd("input param invalid!\n");
                return -1;

            } else {
                if ((action ==  "insert") || (action == "update")) {
                    cv::Mat imgMat = cv::imread(param);
                    const char * ext = strrchr(param.c_str(), '.');

                    if (!ext) {
                        ext = ".png";
                    }

                    cv::imencode(ext, imgMat, imgEnc);
                    uint32_t msg_id = getMsgId();
                    logd("ext:%s,imgEnc.size:%d!\n", ext, imgEnc.size());
                    j = {
                        {"msg_id", msg_id},
                        {"img_id", imgId.c_str()},
                        {"action", action.c_str()},
                        {"mode", mode.c_str()},
                        {
                            "data", {
                                {"img_width", imgMat.cols},
                                {"img_height", imgMat.rows},
                                {"img_data", imgEnc},
                            }
                        }
                    };

                } else {
                    logd("unkown action:%s!\n", action.c_str());
                    return -1;
                }
            }

            /* 向aps发起请求 */
            try {
                std::string msg = j.dump();
                logi("aps.faceid_register_json.v1]%s!\n", msg.c_str());
                mLibflowServer->send("aps.faceid_register_json.v1", msg.c_str(), msg.length());
                {
                    logi("%s!\n", msg.c_str());
                    return 0;
                }

            } catch (json::exception & e) {
                loge("%s\n", e.what());
            }

            return -1;
        }

        int faceRegisterReq(std::string &imgId, std::string &action, std::string &mode)
        {
            const char              *imgBase64;
            std::vector<uint8_t>    imgEnc;
            json j;

            if (action.length() <= 0) {
                logd("input action invalid!\n");
                return -1;

            } else if (imgId.length() <= 0) {
                logd("input imgId invalid!\n");
                return -1;

            } else {
                if ((action == "insert") || (action == "update")) {
                    if (mode == "camera") {
                        uint32_t msg_id = getMsgId();
                        j = {
                            {"msg_id", msg_id},
                            {"img_id", imgId.c_str()},
                            {"action", action.c_str()},
                            {"mode", mode.c_str()},
                        };
                    }

                }  else if (action == "delete") {
                    uint32_t msg_id = getMsgId();

                    if (mode == "all") {
                        j = {
                            {"msg_id", msg_id},
                            {"img_id", mode.c_str()},
                            {"action", action.c_str()},
                            {"mode", mode.c_str()},
                        };

                    } else {
                        j = {
                            {"msg_id", msg_id},
                            {"img_id", imgId.c_str()},
                            {"action", action.c_str()},
                            {"mode", mode.c_str()},
                        };
                    }

                } else {
                    logd("unkown action %s!\n", action.c_str());
                    return -1;
                }
            }

            /* 向aps发起请求 */
            try {
                std::string msg = j.dump();
                logi("tpic[aps.faceid_register_json.v1]%s!\n", msg.c_str());
                mLibflowServer->send("aps.faceid_register_json.v1", msg.c_str(), msg.length());
                {
                    //logi("%s!\n", msg.c_str());
                    return 0;
                }

            } catch (json::exception & e) {
                loge("%s\n", e.what());
            }

            return -1;
        }

        /* 请求驾驶员认证 */
        int faceMatchReq()
        {
            json j = {
                {"action", "distinguish"}
            };
            std::string msg = j.dump();

            logd("send json : %s\n", msg.c_str());

            mLibflowServer->send("aps.faceid_distinguish_json.v1", msg.data(), msg.length());

            return 0;
        }

    private:
        LibflowServer *mLibflowServer;
};

class AdasProt : public AlgoProt
{
    public:
        AdasProt(struct MediaParam & param)
            : AlgoProt("adas", "127.0.0.1", "24012", "*"/*"sdk.output.warning"*/, param)
        {
        }

        ~AdasProt()
        {
        }

        // 处理算法报警数据
        virtual void onAlgoMsg(const char *source,  // '\0' terminated string
                               const char *topic,   // any binary data
                               const char *data,    // any binary data
                               size_t size)        // < 2^32
        {
            AlgoManager & am = AlgoManager::getInstance();
            Manager & m = Manager::getInstance();

            if (!strcmp(topic, "sdk.output.warning")) {
                logi("algo %s json : %s\n", AlgoName(), data);
                std::string recv_str(data, size);

                try {
                    //以json格式解析协议
                    json recv_json = json::parse(recv_str);
                    int frame_id = recv_json.at(AlgoName()).at("frame_id");
                    uint64_t ts  = recv_json.at(AlgoName()).at("time_ms");
                    double   spd = recv_json.at(AlgoName()).at("speed");
                    //获取报警项目
                    std::vector<std::string> alerts_list = recv_json.at(AlgoName()).at("alerts");

                    if (alerts_list.empty()) {
                        //logd("Frame ID '%d' empty\n", frame_id);

                    } else {
                        logd(" Frame ID '%d' get '%d' alerts\n", frame_id, alerts_list.size());

                        for (std::string e : alerts_list) {
                            EVT_TYPE typ = name2value("adas", e.c_str());
                            logw(" alerts '%s', typ = %d\n", e.c_str(), typ);
                            m.mpLogger->mlog("%s > %s", __FUNCTION__, e.c_str());

                            if (typ == EVT_TYPE_ADAS_HW) {
                                float lateral_dist = recv_json.at(AlgoName()).at("longitudinal_dist");
                                float hwValue = static_cast<float>(lateral_dist * 10);
                                logd("lateral_dist = %f hwValue = %f", lateral_dist, hwValue); 
                                char temp[10] = {0};
                                int iVal = static_cast<int>(hwValue);
                                sprintf(temp, "%d", iVal);
                                __system_property_set("rw.algo.hw_dist", temp);
                                m.mpLogger->mlog("hmwDist = %d", iVal);
                            } else if (typ == EVT_TYPE_ADAS_LeftLDW || typ == EVT_TYPE_ADAS_RightLDW) {
                                if (m.current->sensor.left_turn || m.current->sensor.right_turn) {
                                    logd("skip ldw alert turn = %d %d", m.current->sensor.left_turn, m.current->sensor.right_turn);
                                    return ;
                                }
                            }

                            if (EVT_TYPE_INVALID != typ) {
                                if (EVT_TYPE_OVER_HEIGHT == typ) {
                                    /* 限高 */
                                    std::shared_ptr<Event> sp = make_shared<Event>("adas", EVT_TYPE_OVER_HEIGHT);
                                    sp->c.event = "overHeightStart";
                                    sp->c.frameId = frame_id;
                                    sp->c.ts = ts;
                                    sp->c.speed = spd;
                                    am.push(sp);

                                } else {
                                    std::shared_ptr<Event> sp = make_shared<Event>(AlgoName(), typ);
                                    sp->c.event = e;
                                    sp->c.frameId = frame_id;
                                    sp->c.ts = ts;
                                    sp->c.speed = spd;
                                    //int warn_id = recv_json.at(AlgoName()).at("warn_id");
                                    am.push(sp);
                                }
                            }
                        }
                    }

                } catch (json::exception & e) {
                    loge("%s\n", e.what());
                }

            } else if (strstr(topic, "output.adas.algo")) {
                /* 消息内含限高值 */
                //logi("algo %s json : %s\n", AlgoName(), data);
                //logi("algo %s topic:%s json : %s\n", AlgoName(), topic, data);
                std::string recv_str(data, size);

                try {
                    //以json格式解析协议
                    json recv_json = json::parse(recv_str);
                    std::string msg_type = recv_json.at("msg_type");

                    if (msg_type == "tsr_warning_filtered") {
                        double speed_limit = recv_json.at("speed_limit");
                        double weight_limit  = recv_json.at("weight_limit");
                        double height_limit = recv_json.at("height_limit");
                        int iVal = 0;

                        if (speed_limit > 1) {
                            char temp[10] = {0};
                            iVal = (int)(speed_limit * 100);
                            sprintf(temp, "%d", iVal);
                            __system_property_set("rw.algo.speed_limit", temp);

                        } else {
                            //logd("rw.algo.speed_limit:%lf!\n", speed_limit);
                        }

                        if (weight_limit > 1) {
                            char temp[10] = {0};
                            iVal = (int)(weight_limit * 100);
                            sprintf(temp, "%d", iVal);
                            __system_property_set("rw.algo.weight_limit", temp);

                        } else {
                            //logd("rw.algo.weight_limit:%lf!\n", weight_limit);
                        }

                        if (height_limit > 1) {
                            char temp[10] = {0};
                            iVal = (int)(height_limit * 1000);
                            sprintf(temp, "%d", iVal);
                            __system_property_set("rw.algo.height_limit", temp);
                            logd("rw.algo.height_limit:%lf!\n", height_limit);

                        } else {
                            //logd("rw.algo.height_limit:%lf!\n", height_limit);
                        }
                    }else if (msg_type == "vehicle_warning_filtered") {
                        //float headway = recv_json.at("headway");
                        //int32_t hwmLevel = recv_json.at("warning_level");
                        //logd("headway = %f level %d", headway, hwmLevel);

                    }
                } catch (json::exception & e) {
                    loge("%s\n", e.what());
                    logi("algo %s topic:%s json : %s!\n", AlgoName(), topic, data);
                }

            } else if (!strcmp(topic, "output.can.0x700")) { // 处理ADAS 700消息，用于发送给hostio刷新小显示器
                /* 移动到manager.cpp中处理EVT_TYPE_ADAS_CAN700_MSG */

                char propValue[PROP_VALUE_MAX] = {0};
                if (__system_property_get("rw.algo.can.display.output.proc", propValue) > 0) {
                    std::ifstream ifs("/proc/self/comm");
                    std::string comm;
                    ifs >> comm;
                    ifs.close();
                    if (strlen(propValue) > 0 && comm != propValue) {
                        return;
                    }
                }

                uint64_t *pCanByte;
                pCanByte = (uint64_t *)data;
                // logd(" output.can.0x700 size %d data %llx\n", size, *pCanByte);
                // 发送给hostio
                m.sendMcuMsg0x700(*pCanByte);
            } else if (!strcmp(topic, "lane_detect.zebra")) {
                std::vector<RoadMarkHub> roadMark;
                msgpack::object_handle oh = msgpack::unpack(data, size);
                msgpack::object deserialized = oh.get();
                try {
                    deserialized.convert(roadMark);
                    for (RoadMarkHub & rm : roadMark) {
                        if (((STOP_LINE == rm.type) || (ZEBRA_LINE == rm.type)) &&
                            ((0 < rm.dist) && (rm.dist <= 4.0)) /*&& rm.Judge()*/) {
                            if (rm.validWidth(2)) {
                                logd("+onCross, id %d type %d dist %f", rm.id, rm.type, rm.dist);
                                EVT_TYPE type = EVT_TYPE_INVALID;
                                type = EVT_TYPE_ADAS_RCF;
                                std::shared_ptr<minieye::AMessage> amsg = std::make_shared<minieye::AMessage>();
                                //amsg->setString("transfer", transfer.c_str(), transfer.length());
                                //amsg->setFloat("speed", speed);
                                amsg->setWhat(type);
                                m.postMsg(amsg);
                            } else {
                                //logd("-OnCross, id %d type %d dist %f", rm.id, rm.type, rm.dist);
                            }
                         }
                    }
                    
                } catch(...) {
                    logd("msgpack unpack fail");
                }

            } else if (!strcmp(topic, "output.adas.warning")) {
                msgpack::object_handle oh = msgpack::unpack(data, size);
                msgpack::object deserialized = oh.get();
                auto start_ptr = deserialized.via.map.ptr;
                auto end_ptr = deserialized.via.map.ptr + deserialized.via.map.size;
                std::string event;
                std::string trans;
                float speed;
                uint64_t ts = 0;
                while (start_ptr != end_ptr) {
                    string s;
                    start_ptr->key.convert(s);
                    //logd("%s", s.c_str());
                    if(s == "event") {
                        start_ptr->val.convert(event);
                    } else if (s == "transfer") {
                        start_ptr->val.convert(trans);
                    } else if (s == "time") {
                        start_ptr->val.convert(ts);
                    } else if (s == "speed") {
                        start_ptr->val.convert(speed);
                    }
                    start_ptr++;
                }
                if (event != "FCW" && event != "HeadwayWarning") {
                    logd("event %s transfer %s speed %f ts %lld", event.c_str(), trans.c_str(), speed, ts);
                }

                EVT_TYPE type = EVT_TYPE_INVALID;
                // 斑马线超速
                if (event == "ZCO" && trans == "t01") {
                    type = EVT_TYPE_ADAS_ZCO;
                } 
                // 斑马线未礼让行人
                else if (event == "UnfriendlyForPed" && trans == "t01") {
                    type = EVT_TYPE_ADAS_UFPed;   
                } else if (event == "RCF") {
                    type = EVT_TYPE_ADAS_RCF;
                    std::shared_ptr<minieye::AMessage> amsg = std::make_shared<minieye::AMessage>();
                    amsg->setString("transfer", trans.c_str(), trans.length());
                    amsg->setFloat("speed", speed);
                    amsg->setWhat(type);
                    // m.postMsg(amsg);
                    return;
                }
                else {
                    return;
                }
                
                std::shared_ptr<Event> sp = make_shared<Event>(AlgoName(), type);
                sp->c.event = event;
                sp->c.ts = my::timestamp::milliseconds_from_19700101();//ts; 
                sp->c.speed = speed;
                am.push(sp);
            }
        }
};

class BsdProtBase : public AlgoProt
{
    public:
        BsdProtBase(const char * name, const char * server, const char * port, const char * topic, MediaParam & param)
            : AlgoProt(name, server, port, topic, param)
        {
            char propValue[PROP_VALUE_MAX] = {0};
            char propName[64] = {0};
            __system_property_get(PROP_PERSIST_BSD_CAN1DISPLAY, propValue);
            mbCan1display = !strcmp(propValue, "true");

            propValue[0] = 0;
            snprintf(propName, sizeof(propName), PROP_PERSIST_BSD_RIGHT_SRC_CH, name);
            if (__system_property_get(propName, propValue) > 0) {
                mRightChn = atoi(propValue);;
            }
            propValue[0] = 0;
            snprintf(propName, sizeof(propName), PROP_PERSIST_BSD_LEFT_SRC_CH, name);
            if (__system_property_get(propName, propValue) > 0) {
                mLeftChn = atoi(propValue);
            }
        }

        ~BsdProtBase()
        {
        }

        // 处理算法报警数据
        virtual void onAlgoMsg(const char *source,  // '\0' terminated string
                               const char *topic,   // any binary data
                               const char *data,    // any binary data
                               size_t size)        // < 2^32
        {
            AlgoManager & am = AlgoManager::getInstance();
            Manager & m = Manager::getInstance();

            if (!strcmp(topic, "sdk.output.warning")) {
                logi("algo %s json : %s\n", AlgoName(), data);
                std::string recv_str(data, size);

                try {
                    //以json格式解析协议
                    json recv_json = json::parse(recv_str);
                    int frame_id = recv_json.at(AlgoName()).at("frame_id");
                    uint64_t ts  = recv_json.at(AlgoName()).at("time_ms");
                    double   spd = recv_json.at(AlgoName()).at("speed");
                    std::string aName = AlgoName();
                    //获取报警项目
                    std::vector<std::string> alerts_list = recv_json.at(AlgoName()).at("alerts");

                    if (alerts_list.empty()) {
                        //logd(" Frame ID '%d' empty\n", frame_id);

                    } else {
                        logd("[%s] Frame ID '%d' get '%d' alerts\n", aName.c_str(), frame_id, alerts_list.size());

                        for (std::string e : alerts_list) {
                            EVT_TYPE typ = name2value(AlgoName(), e.c_str());
                            //value2voice(typ);
                            logw("[%s] alerts '%s', typ = %d\n", aName.c_str(), e.c_str(), typ);

                            if (EVT_TYPE_INVALID != typ) {
                                std::shared_ptr<Event> sp = make_shared<Event>(aName.c_str(), typ);
                                sp->c.event = e;
                                sp->c.frameId = frame_id;
                                sp->c.ts = ts;
                                sp->c.speed = spd;
                                //int warn_id = recv_json.at(AlgoName()).at("warn_id");
                                const char * pBsd = strstr(e.c_str(), "BsdWarningL");

                                if ((pBsd) && (1 == sscanf(pBsd, "BsdWarningL%d", &mWarnLevel))) {
                                    sp->c.level = mWarnLevel;

                                }
                                if (strstr(e.c_str(), "LBsdWarning")) {
                                    sp->c.srcChn = mLeftChn;
                                } else {
                                    sp->c.srcChn = mRightChn;
                                }
                                mWarnTs = my::timestamp::now();;

                                am.push(sp);
                            }
                        }
                    }

                } catch (json::exception & e) {
                    loge("%s\n", e.what());
                }
            }

            if (mbCan1display && m.com_controller && (mSndCanTs.elapsed() >= 100)) {
                if (mWarnTs.elapsed() > 3000) {
                    mWarnLevel = 0;
                    mTargetDist = 0;
                }

                mSndCanTs = my::timestamp::now();
                McuMsgCanT canMsg;
                canMsg.id = 0x15d;
                canMsg.len = 8;
                canMsg.data[0] = (m.current->sensor.left_turn) | (m.current->sensor.right_turn << 1) | (m.current->sensor.brake << 4);
                canMsg.data[1] = m.current->getSpeed();
                canMsg.data[2] = 0;
                canMsg.data[3] = 0;
                canMsg.data[4] = mWarnLevel;
                canMsg.data[5] = mTargetDist;
                canMsg.data[6] = 1;
                canMsg.data[7] = 0;
                m.com_controller->sendMcuMsgCan1Disp(canMsg);
            }
        }
    protected:
        bool mbCan1display = false;
        int mWarnLevel = 0;
        int mTargetDist = 0;
        my::timestamp mWarnTs;
        my::timestamp mSndCanTs;
    protected:
        int32_t mLeftChn = -1;
        int32_t mRightChn = -1;
        int32_t mFrontChn = -1;
        int32_t mRearChn = -1;
};

class BsdProt : public BsdProtBase
{
    public:
        BsdProt(MediaParam & param)
            : BsdProtBase("bsd", "127.0.0.1", "24013", "*"/*"sdk.output.warning"*/, param)
        {
        }

        ~BsdProt()
        {
        }
};
class FBsdProt : public BsdProtBase
{
    public:
        FBsdProt(MediaParam & param)
            : BsdProtBase("fbsd", "127.0.0.1", "24014", "*"/*"sdk.output.warning"*/, param)
        {

        }

        ~FBsdProt()
        {
        }

        // 处理算法报警数据
        virtual void onAlgoMsg(const char *source,  // '\0' terminated string
                               const char *topic,   // any binary data
                               const char *data,    // any binary data
                               size_t size)        // < 2^32
        {
            std::string aName = AlgoName();
            AlgoManager & am = AlgoManager::getInstance();
            Manager & m = Manager::getInstance();

            if (!strcmp(topic, "output.adas.warning")) {
                msgpack::object_handle oh = msgpack::unpack(data, size);
                msgpack::object deserialized = oh.get();
                auto start_ptr = deserialized.via.map.ptr;
                auto end_ptr = deserialized.via.map.ptr + deserialized.via.map.size;
                std::string evt;
                std::string trans;
                uint64_t ts = 0;

                while (start_ptr != end_ptr) {
                    string s;
                    start_ptr->key.convert(s);

                    logd("%s", s.c_str());

                    if (s == "transfer") {
                        start_ptr->val.convert(trans);
                        logd("%s", trans.c_str());

                    } else if (s == "event") {
                        start_ptr->val.convert(evt);
                        logd("%s", evt.c_str());

                    } else if (s == "time") {//ms
                        start_ptr->val.convert(ts);
                        logd("ts=%ld", ts);
                    }

                    start_ptr++;
                }

                EVT_TYPE typ = name2value(AlgoName(), evt.c_str());
                //value2voice(typ);
                logw("[%s] alerts '%s', typ = %d\n", aName.c_str(), evt.c_str(), typ);

                if (EVT_TYPE_INVALID != typ) {
                    if (trans == "t01") {
                        std::shared_ptr<Event> sp = make_shared<Event>(aName.c_str(), typ);
                        sp->c.event = evt;
                        sp->c.frameId = 0;
                        sp->c.ts = ts;//ms
                        sp->c.speed = m.current->getSpeed();
                        //int warn_id = recv_json.at(AlgoName()).at("warn_id");
                        const char * pBsd = strstr(evt.c_str(), "BsdWarningL");

                        if ((pBsd) && (1 == sscanf(pBsd, "BsdWarningL%d", &mWarnLevel))) {
                            sp->c.level = mWarnLevel;
                        }
                        if (strstr(evt.c_str(), "LFBsdWarning")) {
                            sp->c.srcChn = mLeftChn;
                        } else {
                            sp->c.srcChn = mRightChn;
                        }
                        am.push(sp);

                    } else {
                        mWarnLevel = 0;
                        mTargetDist = 0;
                    }
                }
            } else if (!strcmp(topic, "sdk.output.warning")) { /*right bsd msg*/
                logi("algo %s json : %s fake bsd\n", AlgoName(), data);
                const char * algoName = "bsd";
                std::string recv_str(data, size);

                try {
                    //以json格式解析协议
                    json recv_json = json::parse(recv_str);
                    int frame_id = recv_json.at(algoName).at("frame_id");
                    uint64_t ts  = recv_json.at(algoName).at("time_ms");
                    double   spd = recv_json.at(algoName).at("speed");
                    std::string aName = algoName;
                    //获取报警项目
                    std::vector<std::string> alerts_list = recv_json.at(algoName).at("alerts");

                    if (alerts_list.empty()) {
                        //logd(" Frame ID '%d' empty\n", frame_id);

                    } else {
                        logd("[%s] Frame ID '%d' get '%d' alerts\n", aName.c_str(), frame_id, alerts_list.size());

                        for (std::string e : alerts_list) {
                            EVT_TYPE typ = name2value(algoName, e.c_str());
                            //value2voice(typ);
                            logw("[%s] alerts '%s', typ = %d\n", aName.c_str(), e.c_str(), typ);

                            if (EVT_TYPE_INVALID != typ) {
                                std::shared_ptr<Event> sp = make_shared<Event>(aName.c_str(), typ);
                                sp->c.event = e;
                                sp->c.frameId = frame_id;
                                sp->c.ts = ts;
                                sp->c.speed = spd;
                                //int warn_id = recv_json.at(AlgoName()).at("warn_id");
                                const char * pBsd = strstr(e.c_str(), "BsdWarningL");

                                if ((pBsd) && (1 == sscanf(pBsd, "BsdWarningL%d", &mWarnLevel))) {
                                    sp->c.level = mWarnLevel;
                                }
                                if (strstr(e.c_str(), "LFBsdWarning")) {
                                    sp->c.srcChn = mLeftChn;
                                } else {
                                    sp->c.srcChn = mRightChn;
                                }
                                mWarnTs = my::timestamp::now();;

                                am.push(sp);
                            }
                        }
                    }

                } catch (json::exception & e) {
                    loge("%s\n", e.what());
                }
            }

            if (mbCan1display && m.com_controller && (mSndCanTs.elapsed() >= 100)) {
                mSndCanTs = my::timestamp::now();
                McuMsgCanT canMsg;
                canMsg.id = 0x15d;
                canMsg.len = 8;
                canMsg.data[0] = (m.current->sensor.left_turn) | (m.current->sensor.right_turn << 1) | (m.current->sensor.brake << 4);
                canMsg.data[1] = m.current->getSpeed();
                canMsg.data[2] = 0;
                canMsg.data[3] = 0;
                canMsg.data[4] = mWarnLevel;
                canMsg.data[5] = mTargetDist;
                canMsg.data[6] = 1;
                canMsg.data[7] = 0;
                m.com_controller->sendMcuMsgCan1Disp(canMsg);
            }
        }

};
class ABsdProt: public BsdProtBase
{
    public:
        ABsdProt(MediaParam & param)
            : BsdProtBase("absd", "127.0.0.1", "24025", "*"/*"sdk.output.warning"*/, param)
        {

        }

        // 处理算法报警数据
        virtual void onAlgoMsg(const char *source,  // '\0' terminated string
                               const char *topic,   // any binary data
                               const char *data,    // any binary data
                               size_t size)        // < 2^32
        {
            AlgoManager & am = AlgoManager::getInstance();
            Manager & m = Manager::getInstance();

            if (!strcmp(topic, "output.absd.warning")) { /*absd msg*/
                logi("algo %s json : %s\n", mRealAlgoName.c_str(), data);
                const char * algoName = "absd";
                std::string recv_str(data, size);

                try {
                    //以json格式解析协议
                    json recv_json = json::parse(recv_str);
                    int frame_id = recv_json.at(algoName).at("frame_id");
                    uint64_t ts  = recv_json.at(algoName).at("time_ms");
                    double   spd = recv_json.at(algoName).at("speed");
                    std::string dir = recv_json.at(algoName).at("type");
                    if (!mRealAlgoName.length() && dir.length()) {
                        mRealAlgoName = "absd_";
                        mRealAlgoName += dir;
                    }

                    //获取报警项目
                    std::vector<std::string> alerts_list = recv_json.at(algoName).at("alerts");

                    if (alerts_list.empty()) {
                        //logd(" Frame ID '%d' empty\n", frame_id);

                    } else {
                        logd("[%s] Frame ID '%d' get '%d' alerts\n", mRealAlgoName.c_str(), frame_id, alerts_list.size());

                        for (std::string e : alerts_list) {
                            EVT_TYPE typ = name2value(mRealAlgoName.c_str(), e.c_str());
                            //value2voice(typ);
                            logw("[%s] alerts '%s', typ = %d\n", mRealAlgoName.c_str(), e.c_str(), typ);

                            if (EVT_TYPE_INVALID != typ) {
                                std::shared_ptr<Event> sp = make_shared<Event>(mRealAlgoName.c_str(), typ);
                                sp->c.event = e;
                                sp->c.frameId = frame_id;
                                sp->c.ts = ts;
                                sp->c.speed = spd;
                                //int warn_id = recv_json.at(algoName).at("warn_id");
                                const char * pBsd = strstr(e.c_str(), "BsdWarningL");

                                if ((pBsd) && (1 == sscanf(pBsd, "BsdWarningL%d", &mWarnLevel))) {
                                    sp->c.level = mWarnLevel;
                                }

                                mWarnTs = my::timestamp::now();;

                                am.push(sp);
                            }
                        }
                    }

                } catch (json::exception & e) {
                    loge("%s\n", e.what());
                }
            }
        }
    private:
        std::string mRealAlgoName;
};
class DumperCamProt : public AlgoProt
{
    public:
        DumperCamProt(struct MediaParam & param)
            : AlgoProt("dumper_camera", "127.0.0.1", "24021", "*", param)
        {
        }

        ~DumperCamProt()
        {
        }

        // 处理算法报警数据
        virtual void onAlgoMsg(const char *source,  // '\0' terminated string
                               const char *topic,   // any binary data, dumper.content.result.v1.0.0
                               const char *data,    // any binary data
                               size_t size)        // < 2^32
        {
#if 1
            AlgoManager & am = AlgoManager::getInstance();
            uint64_t ts = 0;//us
            int frame_id = 0;
            int clear = 0;//图像是否清晰 // 0图像不清楚 1图像清楚
            int cover = 0; // kDumperCoverClose = 0, kDumperCoverOpen = 1, kDumperCoverMiddle = 2, kDumperCoverNotSure = 3//0合盖 1开盖 2半开盖 3不确定
            int load = 0;//是否有装渣土// kDumperLoadEmpty = 0, kDumperLoadFull = 1, kDumperLoadMiddle = 2, kDumperLoadNotSure = 3//0空载 1满载 2半载 3不确定
            int iType = 0;//渣土种类

            msgpack::object_handle oh = msgpack::unpack(data, size);
            msgpack::object deserialized = oh.get();
            auto start_ptr = deserialized.via.map.ptr;
            auto end_ptr = deserialized.via.map.ptr + deserialized.via.map.size;
            int version = 0;

            if (!strcmp(topic, "dumper.content.result.v1.0.0")) {
                version = 1;
            }

            while (start_ptr != end_ptr) {
                string s;
                start_ptr->key.convert(s);

                if (s == "time") {
                    ts = start_ptr->val.convert();

                } else if (s == "frame_id") {
                    frame_id = start_ptr->val.convert();

                } else if (s == "clear") {
                    if (version) {
                        clear = start_ptr->val.convert();

                    } else {
                        bool c = start_ptr->val.convert();
                        clear = c;
                    }

                } else if (s == "cover") {
                    if (version) {
                        cover = start_ptr->val.convert();

                    } else {
                        bool c = start_ptr->val.convert();
                        cover = c;
                    }

                } else if (s == "load") {
                    if (version) {
                        load = start_ptr->val.convert();

                    } else {
                        bool l = start_ptr->val.convert();
                        load = l;
                    }

                } else if (s == "type") {
                    iType = start_ptr->val.convert();
                }

                start_ptr++;
            }

            std::shared_ptr<Event> sp = make_shared<Event>("dumper", EVT_TYPE_DUMPER_DETAIL);
            Event * ep = sp.get();
            ep->c.event = "dumper_camera";
            ep->c.frameId = frame_id;
            ep->c.u.dumper.clear = clear;
            ep->c.u.dumper.load = !!load;
            ep->c.u.dumper.full = (1 == load);
            ep->c.u.dumper.cover = (0 == cover);
            ep->c.u.dumper.type = iType;
            logd("dumper_camera [%lld] frameid %d, clear %d, load %d, cover %d, type %d",
                 ts, frame_id, clear, load, cover, iType);
            am.push(sp);
#else
            logd("algo %s json : \n%s\n", AlgoName(),  my::hex(my::constr(data, size), true).c_str());
#endif
        }
};

class DumperImuProt : public AlgoProt
{
    public:
        DumperImuProt(struct MediaParam & param)
            : AlgoProt("dumper_imu", "127.0.0.1", "24022", "*"/*"dumper.lift.result"*/, param)
        {
        }

        ~DumperImuProt()
        {
        }

        // 处理算法报警数据
        virtual void onAlgoMsg(const char *source,  // '\0' terminated string
                               const char *topic,   // any binary data
                               const char *data,    // any binary data
                               size_t size)        // < 2^32
        {
#if 1
            AlgoManager & am = AlgoManager::getInstance();
            uint64_t ts = 0;//us
            bool lift = 0;//货箱是否举起

            msgpack::object_handle oh = msgpack::unpack(data, size);
            msgpack::object deserialized = oh.get();
            auto start_ptr = deserialized.via.map.ptr;
            auto end_ptr = deserialized.via.map.ptr + deserialized.via.map.size;

            while (start_ptr != end_ptr) {
                string s;
                start_ptr->key.convert(s);

                if (s == "time") {
                    ts = start_ptr->val.convert();

                } else if (s == "lift") {
                    lift = start_ptr->val.convert();
                }

                start_ptr++;
            }

            std::shared_ptr<Event> sp = make_shared<Event>("dumper", EVT_TYPE_DUMPER_DETAIL);
            Event * ep = sp.get();
            ep->c.event = "dumper_imu";
            ep->c.ts = ts;
            ep->c.u.dumper.lift = lift;
            logd("dumper_imu [%lld] lift %d", ts, lift);
            am.push(sp);
#else
            logd("algo %s json : \n%s\n", AlgoName(),  my::hex(my::constr(data, size), true).c_str());
#endif
        }
};

class DumperTofProt : public AlgoProt
{
    public:
        DumperTofProt(struct MediaParam & param)
            : AlgoProt("dumper_tof", "127.0.0.1", "24023", "*"/*"dumper.container.result"*/, param)
        {
        }

        ~DumperTofProt()
        {
        }

        // 处理算法报警数据
        virtual void onAlgoMsg(const char *source,  // '\0' terminated string
                               const char *topic,   // any binary data
                               const char *data,    // any binary data
                               size_t size)        // < 2^32
        {
#if 1
            AlgoManager & am = AlgoManager::getInstance();
            uint64_t ts = 0;//us
            int sadc = 0;
            bool full = 0;//是否装满
            bool cover = 0;//是否盖上盖子

            msgpack::object_handle oh = msgpack::unpack(data, size);
            msgpack::object deserialized = oh.get();
            auto start_ptr = deserialized.via.map.ptr;
            auto end_ptr = deserialized.via.map.ptr + deserialized.via.map.size;

            while (start_ptr != end_ptr) {
                string s;
                start_ptr->key.convert(s);

                if (s == "time") {
                    ts = start_ptr->val.convert();

                } else if (s == "sadc") {
                    sadc = start_ptr->val.convert();

                } else if (s == "full") {
                    full = start_ptr->val.convert();

                } else if (s == "close") {
                    cover = start_ptr->val.convert();
                }

                start_ptr++;
            }

            std::shared_ptr<Event> sp = make_shared<Event>("dumper", EVT_TYPE_DUMPER_DETAIL);
            Event * ep = sp.get();
            ep->c.event = "dumper_tof";
            ep->c.u.dumper.load = full;
            ep->c.u.dumper.cover = cover;
            logd("dumper_tof [%lld] sadc %d, full %d, cover %d", ts, sadc, full, cover);
            //am.push(sp);//no need any more, see DumperCamProt
#else
            logd("algo %s json : \n%s\n", AlgoName(), my::hex(my::constr(data, size), true).c_str());
#endif
        }
};

class VehicleInfo
{
    public:
        bool acc;
        // km/h
        float speed_kmph;

        // 0 - off
        // 1 - left on
        // 2 - right on
        // 3 - some on
        int turn_lamp;

        bool  brake;
        double mileage;

        //time
        my::uint64 time;

        //plate num
        std::string plate;

        // driver iccard
        struct {
            std::string status;//on off null
            std::string id;
            std::string name;
            std::string qc_code;
            std::string ca;
            my::uint    validity_date;
        } iccard;

        //location
        struct {
            char status; // 状态: -1天线断开, 0未定位, 1已定位
            double lat; // 经度
            double lng; // 维度
            double alt; // 高度
            double dir; // 方向角
            uint64_t ts;
        } lbs; // 定位信息

        ImuDataT imu;
    public:
        VehicleInfo() : speed_kmph(0), turn_lamp(0) {}

};

// 结构体和json的转换
inline void to_json(json &j, const VehicleInfo &i)
{
    j = {
        {
            "vehicle_info", {
                {"host_time",   i.time},
                {"acc",         i.acc},
                {"speed_kmph",  i.speed_kmph},
                {"turn_lamp",   i.turn_lamp},
                {"plate_num",   i.plate},
                {"brake",       i.brake},
                {"mileage",     i.mileage},
            }
        },
        {
            "location", {
                {"status",      i.lbs.status},
                {"lat", (int)(i.lbs.lat * 1000000)},
                {"lng", (int)(i.lbs.lng * 1000000)},
                {"alt", (int)i.lbs.alt},
                {"dir", (float)i.lbs.dir},
                {"ts", (uint64_t)i.lbs.ts},
            }
        },
        {
            "ic_card", {
                {"status",      i.iccard.status},
                {"id",          i.iccard.id},
                {"name",        i.iccard.name},
                {"qc_code",     i.iccard.qc_code},
                {"ca",          i.iccard.ca},
                {"validity_date", i.iccard.validity_date},
            }
        },
        {
            "imu", {
                {"gyro",    {i.imu.gyro[0], i.imu.gyro[1], i.imu.gyro[2]}},
                {"accel",   {i.imu.accel[0], i.imu.accel[1], i.imu.accel[2]}},
                {"temp",    i.imu.temp},
                {"ts",      i.imu.timestamp / 1000},/*ms*/
                {"roll",    i.imu.roll},
                {"pitch",   i.imu.pitch},
            }
        }
    };
}
void LibflowRcver::recv(const char* source,  // '\0' terminated string
                        const char* topic,   // any binary data
                        const char* data,    // any binary data
                        size_t size)     // < 2^32
{
    logd("topic %s!\n", topic);

    if (!strcmp(topic, "cmd.snapshot.v1")) {
        std::string recv_str(data, size);

        try {
            //以json格式解析协议
            json recv_json = json::parse(recv_str);
            int chn = recv_json.at("snap").at("channel");
            int seq = mSnapSeq++;
            my::file::mkdir("/mnt/obb/mprot/");
            std::string fileName = "/mnt/obb/mprot/snap_seq";
            fileName += std::to_string(seq) + "_ch";
            fileName += std::to_string(chn) + ".jpg";

            std::string snapcmd = "cmd lms ";
            snapcmd += std::to_string(chn + 1) + " ";
            snapcmd += fileName + " ";
            snapcmd += std::to_string(1280) + " ";
            snapcmd += std::to_string(720) + " ";
            snapcmd += "1";
            logd("trig snap %s!\n", snapcmd.c_str());

            vector<char> resp;
            LogCallProxyCmd::sendReq("media", snapcmd.c_str(), resp);
            {
                std::lock_guard<std::mutex> lock(mMtx);
                mSnapTask[seq] = std::pair<std::string, my::timestamp>(fileName, my::timestamp::now());
            }

        } catch (json::exception & e) {
            loge("%s\n", e.what());
        }
    }
}
void LibflowRcver::run()
{
    prctl(PR_SET_NAME, "flowRcver");

    AlgoManager & am = AlgoManager::getInstance();

    while (!exiting()) {
        auto it = mSnapTask.begin();

        while (it != mSnapTask.end()) {
            std::lock_guard<std::mutex> lock(mMtx);

            my::timestamp &ts = it->second.second;

            if ((ts.elapsed() >= 1000) && !access(it->second.first.c_str(), R_OK)) {
                //send
                if (access("/data/cvImg", R_OK)) {//base64
                    my::file f;
                    int64_t fsize = f.open(it->second.first.c_str(), "r");
                    my::string imgBase64;
                    if (fsize > 0) {
                        my::string buf(fsize);
                        int ret = f.gets((char*)buf.c_str(), (int)fsize);
                        my::constr str(buf.c_str(), ret);
                        imgBase64 = my::base64e(str);
                        f.close();
                        logd("fsize %ld, %d, %d", fsize, imgBase64.length(), ret);
                    }
                    try {
                        json j = {
                            {
                                "snap", {
                                    {"seq", it->first},
                                    {"result", (bool)imgBase64.length()},
                                    {"img_data", std::string(imgBase64.c_str())}
                                }
                            }
                        };
                        std::string msg = j.dump();
                        am.sendFlow("cmd.snapshot.v1", msg.c_str(), msg.length());

                    } catch (json::exception & e) {
                        logd("[algo::exception] %s\n", e.what());
                    }
                } else {//opencv
                    cv::Mat imgMat = cv::imread(it->second.first.c_str());

                    if (!imgMat.data || imgMat.empty()) {
                        logd("imread %s failed", it->second.first.c_str());
                        break;
                    }

                    const char * ext = strrchr(it->second.first.c_str(), '.');

                    if (!ext) {
                        ext = ".jpg";
                    }

                    std::vector<uint8_t> imgEnc;
                    int ret = cv::imencode(ext, imgMat, imgEnc);

                    try {
                        json j = {
                            {
                                "snap", {
                                    {"seq", it->first},
                                    {"result", (bool)(!!imgEnc.size())},
                                    {"img_width", imgMat.cols},
                                    {"img_height", imgMat.rows},
                                    {"img_data", imgEnc}
                                }
                            }
                        };
                        std::string msg = j.dump();
                        am.sendFlow("cmd.snapshot.v1", msg.c_str(), msg.length());

                    } catch (json::exception & e) {
                        logd("[algo::exception] %s\n", e.what());
                    }
                }
                unlink(it->second.first.c_str());
                it = mSnapTask.erase(it);
                continue;

            } else if (ts.elapsed() >= 5000) {
                try {
                    json j = {
                        {
                            "snap", {
                                {"seq", it->first},
                                {"result", false},
                                {"img_width", 0},
                                {"img_height", 0},
                                {"img_data", ""}
                            }
                        }
                    };
                    std::string msg = j.dump();
                    am.sendFlow("cmd.snapshot.v1", msg.c_str(), msg.length());

                } catch (json::exception & e) {
                    logd("[algo::exception] %s\n", e.what());
                }

                unlink(it->second.first.c_str());
                it = mSnapTask.erase(it);
                continue;

            } else {
                break;
            }

            it++;
        }

        usleep(1000000);
    }
}
bool LibflowRcver::start()
{
    if (!mbStart) {
        mbStart = true;
        my::thread::start();
    }

    return mbStart;
}

AlgoManager::AlgoManager()
{
    // 初始化LibflowServer用于喂算法的速度和转向
    mLibflowServer = new LibflowServer("0.0.0.0", "23666", "MINIEYE.VehicleInfo.v1");
    mFlowRcver = new LibflowRcver();

    if (mLibflowServer && mFlowRcver) {
        mLibflowServer->add_receiver(mFlowRcver);
    }

    std::unordered_map<std::string, std::pair<algoCreatorFn, struct MediaParam>> algoSet = {
        {"dms",             {algoCreator<DmsProt>,          MediaParam("y",       1280, 720, 15) }},
        {"hod",             {algoCreator<HodProt>,          MediaParam("y",       1280, 720, 15) }},
        {"faceid",          {algoCreator<DmsFaceIDProt>,    MediaParam("y",       1280, 720, 15) }},
        {"faceid_v2",       {algoCreator<DmsFaceIDProt_V2>, MediaParam("y",       1280, 720, 15) }},
        {"adas",            {algoCreator<AdasProt>,         MediaParam("bgr888",  1280, 720, 15) }},
        {"bsd",             {algoCreator<BsdProt>,          MediaParam("y",       1280, 720, 15) }},
        {"absd_front",      {algoCreator<ABsdProt>,         MediaParam("bgr888",  1280, 720, 15, 24025) }},
        {"absd_rear",       {algoCreator<ABsdProt>,         MediaParam("bgr888",  1280, 720, 15, 24026) }},
        {"absd_left",       {algoCreator<ABsdProt>,         MediaParam("bgr888",  1280, 720, 15, 24027) }},
        {"absd_right",      {algoCreator<ABsdProt>,         MediaParam("bgr888",  1280, 720, 15, 24028) }},
        {"fbsd",            {algoCreator<FBsdProt>,         MediaParam("y",       1280, 720, 15) }},
        {"dumper_camera",   {algoCreator<DumperCamProt>,    MediaParam("y",       1280, 720, 15) }},
        {"dumper_imu",      {algoCreator<DumperImuProt>,    MediaParam("y",       1280, 720, 15) }},
        {"dumper_tof",      {algoCreator<DumperTofProt>,    MediaParam("y",       1280, 720, 15) }},
    };

    for (auto algo : algoSet) {
        Algo * pa = algo.second.first(algo.second.second);

        if (pa) {
            mAlgo[algo.first] = pa;
        }
    }

}

bool AlgoManager::start()
{
    // 启动线程喂数据
    if (!mbStarted) {
        my::thread::start();
        mbStarted = true;
    }

    for (auto algo : mAlgo) {
        if (algo.second) {
            algo.second->start();
        }
    }

    return true;
}

void AlgoManager::run()
{
    if (mLibflowServer == NULL) {
        logw("[AlgoManager::run] mLibflowServer NULL\n");
        return;
    }

    prctl(PR_SET_NAME, "algoMngrVehicle");

    // 启动LibflowServer
    mLibflowServer->start();
    mFlowRcver->start();
    VehicleInfo vehicle_info;
    my::timestamp ts = my::timestamp::now();
    Manager & m = Manager::getInstance();

    while (!exiting()) {
        if (!m.current) {
            loge("m.current is null!\n");
            usleep(1000);
            continue;
        }

        double msElapsed = ts.elapsed();

        if (msElapsed >= 100 || msElapsed < 0) {
            ts = my::timestamp::now();
            Current c = m.current->copy();
            std::vector<ImuDataT> imus;
            m.getImuData(imus);
            // 1、车速和转向速度
            vehicle_info.acc = c.getStateAcc();
            vehicle_info.speed_kmph = c.getSpeed();
            vehicle_info.turn_lamp  = c.sensor.left_turn | (c.sensor.right_turn << 1);
            vehicle_info.brake = c.sensor.brake;
            vehicle_info.mileage = c.car.mileage;
            vehicle_info.iccard.status = (1 == c.ic.state) ? "on" : ((2 == c.ic.state) ? "off" : "null");
            vehicle_info.iccard.id = c.ic.id.c_str();
            vehicle_info.iccard.name = c.ic.name.c_str();
            vehicle_info.iccard.qc_code = c.ic.qc_code.c_str();
            vehicle_info.iccard.ca = c.ic.ca.c_str();
            vehicle_info.iccard.validity_date = c.ic.validity_date;
            //loge("turn_lamp %d\n", vehicle_info.turn_lamp);
            //2. location
            vehicle_info.lbs.status = c.lbs.status;
            vehicle_info.lbs.lat    = c.lbs.lat;
            vehicle_info.lbs.lng    = c.lbs.lng;
            vehicle_info.lbs.alt    = c.lbs.alt;
            vehicle_info.lbs.dir    = c.lbs.dir;
            vehicle_info.lbs.ts     = c.lbs.time;

            //3. 主机时间
            vehicle_info.time = my::timestamp::seconds_from_19700101();

            //4. 车牌号
            char plate[128] = {0};

            my::gbkToUtf8((char*)m.config.sys.vehicle.plate_num.c_str(), plate);

            vehicle_info.plate = plate;

            //5. IMU
            if (imus.size()) {
                vehicle_info.imu = imus.back();
            }

            try {
                json j = vehicle_info;
                std::string str_to_send = j.dump();
                // 喂数据的速度10HZ
                //logd("send : %s", str_to_send.data());
                mLibflowServer->send(str_to_send.data(), str_to_send.length());

            } catch (json::exception & e) {
                if (mLastmLogTm.elapsed() > 10 * 1000) {
                    mLastmLogTm = my::timestamp::now();
                    m.mpLogger->mlog("[algo::exception] %s\n", e.what());
                }
            }

        } else {
            usleep(100000 - msElapsed * 1000);
        }
    }
}

int AlgoManager::sendFlow(const char* topic, const char* data, size_t size)
{
    if (mLibflowServer) {
        mLibflowServer->send(topic, data, size);
        return 0;

    } else {
        return -1;
    }
}


int AlgoManager::faceDetect()
{
    Algo * pa = mAlgo["faceid"];

    if (pa) {
        DmsFaceIDProt * pf = dynamic_cast<DmsFaceIDProt *>(pa);
        return pf->faceDetect();
    }

    return -1;
}

int AlgoManager::faceDetectedGet(const std::string & imgPath)
{
    Algo * pa = mAlgo["faceid"];

    if (pa) {
        DmsFaceIDProt * pf = dynamic_cast<DmsFaceIDProt *>(pa);
        return pf->faceDetectedGet(imgPath);
    }

    return -1;
}

int AlgoManager::faceFeatureReq(std::string & imgId, std::string & imgPath)
{
    Algo * pa = mAlgo["faceid"];

    if (pa) {
        DmsFaceIDProt * pf = dynamic_cast<DmsFaceIDProt *>(pa);
        return pf->faceFeatureReq(imgId, imgPath);
    }

    return -1;
}

int AlgoManager::faceFeatureGet(std::string & imgId, std::vector<uint8_t> & faceFeature)
{
    Algo * pa = mAlgo["faceid"];

    if (pa) {
        DmsFaceIDProt * pf = dynamic_cast<DmsFaceIDProt *>(pa);
        return pf->faceFeatureGet(imgId, faceFeature);
    }

    return -1;
}

int AlgoManager::faceFeatureFindMatchReq(std::vector<uint8_t> & curFeature,
        std::vector<std::pair<std::string/*imgId*/, std::vector<uint8_t>/*feature*/>> & featureList)
{
    Algo * pa = mAlgo["faceid"];

    if (pa) {
        DmsFaceIDProt * pf = dynamic_cast<DmsFaceIDProt *>(pa);
        return pf->faceFeatureFindMatchReq(curFeature, featureList);
    }

    return -1;
}
int AlgoManager::faceFeatureFindMatchGet(
    std::unordered_map<std::string  /*imgId*/, struct FaceIdMatchRes > & result)
{
    Algo * pa = mAlgo["faceid"];

    if (pa) {
        DmsFaceIDProt * pf = dynamic_cast<DmsFaceIDProt *>(pa);
        return pf->faceFeatureFindMatchGet(result);
    }

    return -1;
}

int AlgoManager::faceRegister_V2(std::string imgId, std::string& action, std::string &mode)
{
    Algo * pa = mAlgo["faceid_v2"];

    if (pa) {
        DmsFaceIDProt_V2 * pf = dynamic_cast<DmsFaceIDProt_V2 *>(pa);
        return pf->faceRegisterReq(imgId, action, mode);
    }

    return -1;
}

int AlgoManager::faceRegister_V2(std::string imgId, std::string& action, std::string &mode, std::string &param)
{
    Algo * pa = mAlgo["faceid_v2"];

    if (pa) {
        DmsFaceIDProt_V2 * pf = dynamic_cast<DmsFaceIDProt_V2 *>(pa);
        return pf->faceRegisterReq(imgId, action, mode, param);
    }

    return -1;
}

int AlgoManager::faceMatch_V2()
{
    Algo * pa = mAlgo["faceid_v2"];

    if (pa) {
        DmsFaceIDProt_V2 * pf = dynamic_cast<DmsFaceIDProt_V2 *>(pa);
        return pf->faceMatchReq();
    }

    return -1;
}

int AlgoManager::triggerEvent(std::string name, std::string eventName, double speed)
{
    EVT_TYPE et = name2value(name.c_str(), eventName.c_str());
    std::shared_ptr<Event> e = make_shared<Event>(name.c_str(), et);
    e->c.ts = my::timestamp::milliseconds_from_19700101();
    e->c.speed = speed;
    logd("%s, et = %d", eventName.c_str(), et);
    push(e);
    return 0;
}