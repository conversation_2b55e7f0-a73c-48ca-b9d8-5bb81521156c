#include "mcu.msg.handler.h"
#include "event.h"

SINGLETON_STATIC_INSTANCE(McuMsgHandler);
McuMsgHandler::McuMsgHandler()
{
    init();
}
McuMsgHandler::~McuMsgHandler()
{
    fini();
}
int McuMsgHandler::init()
{
    if (mbInit) {
        return -1;
    }
    mClient = new IpcClient("mprot", SH_NAME_HOSTIO);
    if (mClient) {
        mbInit = true;
        return my::thread::start();
    }
    return -1;
}
void McuMsgHandler::fini()
{
    if (mbInit) {
        my::thread::stop();
        delete mClient;
        mClient = nullptr;
        mbInit = false;
    }
}
void McuMsgHandler::comm_send(McuMsgTypeE cmd, uint8_t *data, int32_t len)
{
    uint8_t buf[MCU_MSG_MAX_SIZE];
    McuMessage *pMsg = (McuMessage *)&buf[0];
    memset(buf, 0, sizeof(buf));
    pMsg->ipcMsg.type = cmd;
    pMsg->ipcMsg.len = len;
    if (data && len) {
        memcpy(&pMsg->u.u8Array[0], data, len);
    }
    mClient->send(&(pMsg->ipcMsg));
}
int McuMsgHandler::set_can_filter2spd_port(McuMsgCanFilterT & filter)
{
    filter.enable = 1;
    filter.canIdx = MCU_CAN_IDX_CAN0_SPEED_RES1;
    filter.useListMode = true;
    comm_send(MCU_MSG_TYPE_SET_CAN_FILTER, (uint8_t *)&filter, sizeof(filter));
    return 0;
}
int McuMsgHandler::send_can_msg2spd_port(McuMsgCanT & canMsg)
{
    comm_send(MCU_MSG_TYPE_RAW_CAN0, (uint8_t *)&canMsg, sizeof(canMsg));
    return 0;
}

int McuMsgHandler::send_can_msg2disp_port(McuMsgCanT & canMsg)
{
    comm_send(MCU_MSG_TYPE_RAW_CAN1, (uint8_t *)&canMsg, sizeof(canMsg));
    return 0;
}
bool McuMsgHandler::on_recv(McuMessage * msg) {
    switch(msg->ipcMsg.type)
    {
        case MCU_MSG_TYPE_TRIP_INFO: {
            //msg->u.trip[0];
            break;
        }
        case MCU_MSG_TYPE_STAT: {
            if (MCU_MSG_SIZE_STAT == msg->ipcMsg.len) {
                //m.current->mMcuStat = msg->u.stat[0];
                pwr_set_t* powerStat = &msg->u.stat[0].pwr_set;
                IOStatus * iostat = &msg->u.stat[0].vehicle_io;
                
            }
            break;
        }
        case MCU_MSG_TYPE_IC_CARD_LOGIN: {
            if (MCU_MSG_SIZE_ICCARD == msg->ipcMsg.len) {
                ICCardWrBuf * icCard = msg->u.icCard;
                //logd("MCU_MSG_TYPE_IC_CARD_LOGIN");
            }
            break;
        }
        case MCU_MSG_TYPE_IC_CARD_LOGOUT: {
            //logd("MCU_MSG_TYPE_IC_CARD_LOGOUT");
            break;
        }
        case MCU_MSG_TYPE_RECORD_STATUS: {

            break;
        }
        case MCU_MSG_TYPE_CAR_INFO: {
            if (MCU_MSG_SIZE_CAR_INFO == msg->ipcMsg.len) {
                McuMsgCarInfoT * carInfo = msg->u.carInfo;
                //logd("MCU_MSG_SIZE_CAR_INFO");
            }
            break;
        }
        case MCU_MSG_TYPE_RAW_CAN0: {
            
            break;
        }
        case MCU_MSG_TYPE_RAW_CAN1: {
            
            break;
        }

        case MCU_MSG_TYPE_GPS_CMD: {/*NMEA RAW data*/
            //todo
            break;
        }
        default: {
            //loge("unknown mcu msg type %d, len %d", msg->type, msg->len);
            break;
        }
    }
    return true;
}

void McuMsgHandler::run()
{
    prctl(PR_SET_NAME, "McuMsgHandler");
    while (!exiting()) {
        uint8_t buf[MCU_MSG_MAX_SIZE];
        McuMessage *pMsg = (McuMessage *)&buf[0];
        //printf("McuMsgHandler read\n");
        if (mClient->recv(&(pMsg->ipcMsg))) {
            on_recv(pMsg);
        }
        else {
            loge("mcu client read fail!\n");
        }
    }
}
