#ifndef __MINIEYE_DEVICE_HUB_H__
#define __MINIEYE_DEVICE_HUB_H__

#include <arpa/inet.h>
#include <netdb.h>
#include <string>
#include <sys/prctl.h>
#include <stdint.h>

#include "transporter.h"
#include "McuMessage.h"


using namespace std;

namespace minieye
{
typedef struct CanFrame {
    uint32_t  frameId;
    uint8_t   len;
    uint8_t   data[8];
} __attribute__ ((packed)) canFrame_t;

class DeviceHub
    : public my::thread
    , public my::Singleton<DeviceHub>
{
    friend class my::Singleton<DeviceHub>;
public:
    enum DeviceType {
        DEVICE_TYPE_UNKNOWN = -1,
        DEVICE_TYPE_485,
        DEVICE_TYPE_CAN0,
        DEVICE_TYPE_CAN1,
        DEVICE_TYPE_MAX
    };

    ~DeviceHub(){}

    int32_t init(void);
    int32_t registerDevice(DeviceType type, int32_t socket);
    double getCarSpeed() {std::lock_guard<std::mutex> lock(mMtx);return mCarSpeed; }
    void setCarSpeed(double speed ) {std::lock_guard<std::mutex> lock(mMtx);mCarSpeed = speed;}
    uint8_t getTurnRight() { std::lock_guard<std::mutex> lock(mMtx);return mTurnR; }
    void setTurnRight(uint8_t val){ std::lock_guard<std::mutex> lock(mMtx);mTurnR = val; }
    uint8_t getTurnLeft() { std::lock_guard<std::mutex> lock(mMtx);return mTurnL; }
    void setTurnLeft(uint8_t val){ std::lock_guard<std::mutex> lock(mMtx);mTurnL = val; }
	uint8_t getReverse() { std::lock_guard<std::mutex> lock(mMtx);return mReverse; }
    void setReverse(uint8_t val){ std::lock_guard<std::mutex> lock(mMtx);mReverse = val; }
	void setDrvTime(int32_t seconds) {std::lock_guard<std::mutex> lock(mMtx);mDrvSeconds = seconds;}
	int32_t getDrvTime() {std::lock_guard<std::mutex> lock(mMtx);return mDrvSeconds;}
    void getGpsLbs(LBS& lbs) {std::lock_guard<std::mutex> lock(mMtx);lbs = mLbs;}
    void setGpsLbs(const LBS& lbs) {std::lock_guard<std::mutex> lock(mMtx);mLbs = lbs;}

    bool setBuadRate(DeviceType type, uint32_t baudrate);
    bool setCanMode(DeviceType type,       std::string baudrate, std::string mode);
    bool setCanFilter(DeviceType type,        uint32_t canId);
    bool setCanFilter(DeviceType type,std::vector<uint32_t> canId);

protected:
    void run();
    int32_t onDeviceHubRecv(DeviceType type, uint8_t *buf, const int32_t size);

    
private:
    DeviceHub(){}
    DeviceHub(const DeviceHub&) = delete;  
    DeviceHub& operator = (const DeviceHub &) = delete;

	// hostIO mcu消息处理器
	class McuMsgHandler : public my::thread
	{
	public:
	    McuMsgHandler()
	    {
	        mClient = new IpcClient("expand", SH_NAME_HOSTIO);
	    }
	    int init(DeviceHub *hub) {
	        mDeviceHub = hub;
	        return my::thread::start();
	    }
	    void deinit() {
	        my::thread::stop();
	    }
		void comm_send(McuMsgTypeE cmd, uint8_t *data, int32_t len)
		{
		    if (cmd == MCU_MSG_TYPE_INVALID) {
                loge("send cmd error");
                return;
            }
			uint8_t buf[MCU_MSG_MAX_SIZE];
			McuMessage *pMsg = (McuMessage *)&buf[0];
			memset(buf, 0, sizeof(buf));
			pMsg->ipcMsg.type = cmd;
			pMsg->ipcMsg.len = len;
			memcpy(&pMsg->u.u8Array[0], data, len);
			mClient->send(&(pMsg->ipcMsg));
		}
	protected:
	    void run()
	    {
	        while (!exiting()) {
	            uint8_t buf[MCU_MSG_MAX_SIZE];
	            McuMessage *pMsg = (McuMessage *)&buf[0];
	            //printf("McuMsgHandler read\n");
	            if (mClient->recv(&(pMsg->ipcMsg))) {
	                on_recv(pMsg);
	            }
	            else {
	                loge("mcu client read fail!\n");
	            }
	        }
	    }

	    bool on_recv(McuMessage * msg);
		
	private:
		IpcClient *mClient;
        DeviceHub *mDeviceHub;
	};

    int32_t writeStream(DeviceType type, uint8_t *buf, const int32_t size);
    int32_t onSocketDataRecv(DeviceType type);


    std::shared_ptr<McuMsgHandler> mcuHandler;
    int32_t mSock[DEVICE_TYPE_MAX];
	std::mutex mMtx;
    double  mCarSpeed = 0;
    uint8_t mTurnL = 0;
    uint8_t mTurnR = 0;
	uint8_t mReverse = 0;
	int32_t mDrvSeconds;
    LBS mLbs;
};


} //namespace minieye
#endif
