#include "weightSensor.h"
#include <assert.h>
#include <sys/types.h>
#include <sys/stat.h>
#include <unistd.h>
#include <sys/time.h>

#include "expand.message.h"
#include "expand.h"
#include <iostream>
#include <stdio.h>
#include <string>
#include "rapidjson.wrap.h"
#include <map>
#include <string.h>
#include <sys/system_properties.h>
#include "idvrProperty.h"
#include "McuMessage.h"
#include "devicehub.h"

namespace minieye {

static int propertySet(const char *key, const char *value)
{
    char data[PROP_VALUE_MAX];
    memset(data, 0, sizeof(data));
    int len = 0; 
    len = __system_property_get(key, data);
    if (len > 0) {
        if (strcmp(data, value)) {
            return __system_property_set(key, value);
        } else {
            /* 内容不变不修改直接返回成功 */
            return 0;
        }
    } else {
        return __system_property_set(key, value);
    }
    return -1;
}


weightSensor_SAHX_120B::weightSensor_SAHX_120B(void)
{
    mStatus = false;
    mParseWeightFlag = true;
    mIsCalib = false;

    pthread_cond_init(&mCond, NULL);
    pthread_mutex_init(&mLock, NULL);
}
weightSensor_SAHX_120B::~weightSensor_SAHX_120B()
{
    pthread_mutex_destroy(&mLock);
    pthread_cond_destroy(&mCond);
}

int32_t weightSensor_SAHX_120B::onServerConnected(void)
{
    start();
    return 0;
}
int32_t weightSensor_SAHX_120B::onServerDisconnected(void)
{
    stop();
    return 0;
}

int32_t weightSensor_SAHX_120B::onDataRecevied(const char *p, uint32_t len)
{
	logd("len %d, recv  %s", len, my::hex(my::constr(p, len)).c_str());

    onCmdData(mRcvArray, p, len);
    return 0;
}

int32_t weightSensor_SAHX_120B::onCmdData(vector<uint8_t> & recvArray, const char *p, uint32_t len)
{
    recvArray.insert(recvArray.end(), p, p + len);

    int32_t parsed = 0;
    do {
        parsed = onDataParse((const char *)recvArray.data(), recvArray.size());
        //assert(parsed <= recvArray.size());
        if (recvArray.size() >= (1<<20)) {
            parsed = recvArray.size();
            logd("frame too long!!\n");
        }
        
        if (parsed < 0) {
            recvArray.erase(recvArray.begin(), recvArray.begin() + recvArray.size());
            parsed = 0;
            //logd("frame no header, erase all!!\n");
        } else if (parsed > 0) {
            recvArray.erase(recvArray.begin(), recvArray.begin() + parsed);
            //logd("erase size %d\n", parsed);
        } else {
            logd("alertor frame not complete!!\n");
        }

        if(recvArray.size() <=0 || parsed <= 0) {
            break;
        }

    } while(1);

    return parsed;

}

int32_t weightSensor_SAHX_120B::onDataParse(const char *p, uint32_t size)
{
    int32_t start = -1;
    int32_t end = -1;
    uint32_t offset = 0;
    for (int i = 0; i < static_cast<int32_t>(size); ++i) {
        if (p[i] == SAHX_120B_BEGIN) {
            start = i + 1;
        }
        if (p[i] == SAHX_120B_END_1 && p[i + 1] == SAHX_120B_END_2) {
            end = i - 1;
        }
    }
 
    if (start > 0 && end > 0) {
        uint8_t* tmp = (uint8_t*)(p + start);
        uint32_t len = end - start + 1;
        
        vector<uint8_t> v;
        onUnConvert((const uint8_t*)tmp, v, len);
        tmp  = v.data();
        size = v.size();
        
        if (onDataValidate((const uint8_t *)tmp, size)) {
            logd("enter onDataValidate");
            onMessage((SAHX_120B_RecvMsg *)tmp, size);
        } else {
            logd("Invalid data");
            return -1;
        }
    } else {
        //logd("******start =  %d, end = %d\n", start, end);
        return -1;
    }
    
    return end + 1;
}

bool weightSensor_SAHX_120B::onUnConvert(const uint8_t* inData, vector<uint8_t>& outData, int len)
{
    uint8_t tmp = 0;

    uint8_t first = 0;
    uint8_t second = 0;

    
    if (inData == NULL || len % 2 != 0 || len < 0) {
        return false;
    }
    for (int offset = 0; offset < len; offset+=2) {
        
        if (inData[offset] >= 48 && inData[offset] <= 57) {
            first = inData[offset] - 48;
        } else if (inData[offset] >= 65 && inData[offset] <= 90) {
            first = inData[offset] - 65 + 10;
        }
        

        if (inData[offset + 1 ] >= 48 && inData[offset + 1] <= 57) {
            second = inData[offset + 1] - 48;
        } else if (inData[offset + 1] >= 65 && inData[offset + 1] <= 90) {
            second = inData[offset + 1] - 65 + 10;
        }

        tmp = first * 16 + second;

        outData.push_back(tmp);
    }
    
    return true;
}


bool weightSensor_SAHX_120B::onConvert(const uint8_t* inData, vector<uint8_t>& outData, int len)
{
    uint8_t value;
    uint8_t first;
    uint8_t second;
    uint8_t tmp1;
    uint8_t tmp2;
    if (NULL == inData) {
        return false;
    }
    
    for (int offset = 0; offset < len; offset++)
    {
        value = inData[offset];

        first = value / 16;
        second = value % 16;
        if (first >= 0x00 && first <= 0x09) {
            tmp1 = 48 + first;
        } else if (first >= 0x0a && first <= 0x0f) {
            tmp1 = 65 + first - 10;
        }

        if (second >= 0x00 && second <= 0x09) {
            tmp2 = 48 + second;
        } else if (second >= 0x0a && second <= 0x0f) {
            tmp2 = 65 + second - 10;
        }
        //logd("%0x %0x %0x", value, tmp1, tmp2);
        outData.push_back(tmp1);
        outData.push_back(tmp2);
    }
    return true;
}

//LRC校验
bool weightSensor_SAHX_120B::onDataValidate(const uint8_t *p, uint32_t len)
{
    uint8_t check = calLRC(p, len - 1);
    if (check == p[len - 1]) {
        return true;
    } else {
        return false;
    }
    
    return true;
}

bool weightSensor_SAHX_120B::onMessage(SAHX_120B_RecvMsg *p, uint32_t len)
{
    uint16_t unit;
    uint32_t result;
    logd("addr %x, funcCode %x, len %x", p->addr, p->funcCode, p->len);
    switch (p->funcCode)
    {
        case SAHX_120B_FUNC_CODE_READ_PARAM:
            memcpy(&unit, p->data, 2);
            mUnit = ntohs(unit);
            logd("unit %d", mUnit);
            pthread_mutex_lock(&mLock);
            pthread_cond_signal(&mCond);
            pthread_mutex_unlock(&mLock);
            break;
        case SAHX_120B_FUNC_CODE_READ_DATA:
            memcpy(&result, p->data, 4);
            if (mParseWeightFlag) {
                mWeight = ntohl(result);
                logd("weight %d", mWeight);
            } else {
                mWeightAD = ntohl(result);
                logd("AD %d", mWeightAD);
            }
            pthread_mutex_lock(&mLock);
            pthread_cond_signal(&mCond);
            pthread_mutex_unlock(&mLock);
            break;
        case SAHX_120B_FUNC_CODE_WRITE_PARAM:
            logd("recv ack, calib success");
            mIsCalib = true;
            
            break;
    }
    return true;
}

int32_t weightSensor_SAHX_120B::sendCmd(uint8_t funcCode, uint8_t id, uint16_t len)
{
    vector<uint8_t> send;
    send.push_back(SAHX_120B_BEGIN);
    
    uint8_t buf[128] = {0};
    vector<uint8_t> v;
    SAHX_120B_Send_Msg *p = (SAHX_120B_Send_Msg *)&buf[0];
    p->addr         = SAHX_120B_ADDR;
    p->funcCode     = funcCode;
    p->ID[0]        = SAHX_120B_ADDR;
    p->ID[1]        = id;
    p->len          = htons(len);
    p->check        = calLRC((const uint8_t *)p, sizeof(SAHX_120B_Send_Msg));
    onConvert((const uint8_t*)p, v, sizeof(SAHX_120B_Send_Msg));
    //
    send.insert(send.end(), v.begin(), v.end());
    
    send.push_back(SAHX_120B_END_1);
    send.push_back(SAHX_120B_END_2);
    
    msgEnque(send.data(), send.size());

    return 0;
}

bool weightSensor_SAHX_120B::sendUnitCmd(void)
{
    bool ret = false;

    sendCmd(SAHX_120B_FUNC_CODE_READ_PARAM, SAHX_120B_MESSAGE_ID_UNIT, SAHX_120B_LENGTH_UNIT);

    ret = wait_ack();

    logd("weight %d, unit %d", mWeight, mUnit);

    return ret;
}

bool weightSensor_SAHX_120B::sendWeightCmd(void)
{
    bool ret = false;
    
    mParseWeightFlag = true;
    sendCmd(SAHX_120B_FUNC_CODE_READ_DATA, SAHX_120B_MESSAGE_ID_WEIGHT, SAHX_120B_LENGTH_WEIGHT);

    ret = wait_ack();

    return ret;
}

bool weightSensor_SAHX_120B::sendADCmd(void)
{
    bool ret = false;
    
    mParseWeightFlag = false;
    sendCmd(SAHX_120B_FUNC_CODE_READ_DATA, SAHX_120B_MESSAGE_ID_AD, SAHX_120B_LENGTH_AD);

    ret = wait_ack();

    return ret;
}

bool weightSensor_SAHX_120B::parseCalibDataFromJson(void)
{
    rapidjson::Document doc;
    uint32_t num = 0;
    int ad;
    int load;
    char name[20] = {0};
    RAPIDJSON_LOAD(CALIB_JSON_PATH);

    RAPIDJSON_GET_JSON_STRING(doc, "sensorType", mSensorType);
    RAPIDJSON_GET_JSON_INT(doc, "data_num", num);
    for (int i = 0; i < num; i++) {
        sprintf(name, "calib%d", i);
        RAPIDJSON_GET_JSON_INT(doc[name], "AD_value", ad);
        RAPIDJSON_GET_JSON_INT(doc[name], "load_value", load);
        mCalibData.insert(std::pair<int, int>(ad, load*1000));
    }
    return true;
}

bool weightSensor_SAHX_120B::dumpCalibData(void)
{
    int index = 0;
    logd("sensorType:%s", mSensorType.c_str());
    for(std::map <int, int> ::iterator iter = mCalibData.begin(); iter != mCalibData.end(); iter++)
    {
        logd("%d:%d", iter->first, iter->second);
    }
    return true;
}

//
bool weightSensor_SAHX_120B::sendCalibCmd(void)
{
    vector<uint8_t> send;
    send.push_back(SAHX_120B_BEGIN);
    
    uint8_t buf[128] = {0};
    vector<uint8_t> v;
    SAHX_120B_Clib_Msg *p = (SAHX_120B_Clib_Msg *)&buf[0];
    p->addr         = SAHX_120B_ADDR;
    p->funcCode     = SAHX_120B_FUNC_CODE_WRITE_PARAM;
    p->ID[0]        = SAHX_120B_ADDR;
    p->ID[1]        = SAHX_120B_MESSAGE_ID_CALIB;
    p->dataNum      = htons(0x28);
    p->byteNum      = 0x50;//htons(0x50);
    p->totalNum     = htons(0x01);
    p->index        = htons(0x00);
    int idx = 0;
    for(std::map <int, int> ::iterator iter = mCalibData.begin(); iter != mCalibData.end(); iter++) {
        p->data[idx].AD = htonl(iter->first);
        p->data[idx].weight = htonl(iter->second);
        idx++;
    }

    for (int i = idx; i < 10; i++) {
        p->data[i].AD = 0xffffffff;
        p->data[i].weight = 0xffffffff;
    }

    p->check = calLRC((const uint8_t *)p, sizeof(SAHX_120B_Clib_Msg));
    onConvert((const uint8_t*)p, v, sizeof(SAHX_120B_Clib_Msg));
    //
    send.insert(send.end(), v.begin(), v.end());
    
    send.push_back(SAHX_120B_END_1);
    send.push_back(SAHX_120B_END_2);
    
    msgEnque(send.data(), send.size());
    
    return true;
}


bool weightSensor_SAHX_120B::sendFlowMessage(void)
{
    expand::weightSensorMessage message;
    message.mUtcTime = my::timestamp::utc_milliseconds();
    message.mUnit = mUnit;
    message.mWeight = mWeight;
    message.mWeightAD = mWeightAD;
    message.mStatus = mStatus;
    message.mIsCalib = mIsCalib;

    msgpack::sbuffer sbuf;
    msgpack::pack(sbuf, message);
    ExpandSet::getInstance().sendLibFlow(WEIGHTSENSOR_LIBFLOW_TOPIIC, sbuf.data(), sbuf.size());
    return true;
}

void weightSensor_SAHX_120B::run()
{
    uint32_t noAckCount = 0;
    bool ret = false;
    while(!exiting())
    {

        if (noAckCount > 5) {
            noAckCount = 0;
            mStatus = false;
            sendFlowMessage();
        }
        //AD值
        ret = sendADCmd();
        if (ret) {
            noAckCount = 0;
            mStatus = true;
        } else {
            noAckCount++;
            msleep(100);
            continue;
        }
        
        msleep(100);//消息发送间隔至少100ms
        
        //重量单位
        ret = sendUnitCmd();
        if (ret) {
            noAckCount = 0;
            mStatus = true;
        } else {
            noAckCount++;
            msleep(100);
            continue;
        }
        
        msleep(100);//消息发送间隔至少100ms
        
        //实际载重值
        ret = sendWeightCmd();
        if (ret) {
            noAckCount = 0;
            mStatus = true;
        } else {
            noAckCount++;
            msleep(100);
            continue;
        }


        sendFlowMessage();
        sleep(5);
    }

}

bool weightSensor_SAHX_120B::wait_ack(void)
{
    struct timeval now;
    struct timespec timeout;
    int retcode = 0;

    pthread_mutex_lock(&mLock);
    
    gettimeofday(&now, NULL);
    timeout.tv_sec = now.tv_sec + 1;
    timeout.tv_nsec = now.tv_usec * 1000;
    retcode = pthread_cond_timedwait(&mCond, &mLock, &timeout);

    pthread_mutex_unlock(&mLock);
    if (retcode == ETIMEDOUT) {
        /* timeout occurred */
        return false;
    } else {
        /* operate on x and y */
        return true;
    }
    return true;
}


bool weightSensor_SAHX_120B::runCmd(int argc, char **argv, string &ack)
{
    bool wait = false;
    mInfo = "";
    uint8_t buf[32] = {0};
    bool ret = false;

    if (argc < 1) {
		ack = "arg error";
        return false;
    }
	if (!strcmp(argv[0], "help")) {
		ack = "\n cmd getAD\n cmd getWeight\n cmd setCalib\n cmd getCalib\n cmd getStatus\n";
		return true;
	} else if (!strcmp(argv[0], "getAD")) {
		sendADCmd();
		char buf[32] = {0};
		sprintf(buf, "ad is %d", mWeightAD);
		ack = buf;
		return true;
	} else if (!strcmp(argv[0], "getWeight")) {
		sendWeightCmd();
		char buf[32] = {0};
		sprintf(buf, "weight is %d", mWeight);
		ack = buf;
		return true;
	} else if (!strcmp(argv[0], "setCalib")) {
    	mIsCalib = false;
        mCalibData.clear();
        parseCalibDataFromJson();
        dumpCalibData();
        sendCalibCmd();
        if (mIsCalib) {
            ack = "setCalib ok";
        }
    } else if (!strcmp(argv[0], "getCalib")) {
        if (mIsCalib) {
            ack = "already Calib";
        } else {
            ack = "not Calib";
        }
    } else if (!strcmp(argv[0], "getStatus")) {
        if (mStatus) {
            ack = "connect";
        } else {
            ack = "not connect";
        }
    }

    ack = "OK";

    return true;
}

uint8_t weightSensor_SAHX_120B::calLRC(const uint8_t *ch, uint32_t len)
{
    uint32_t result = 0;
    uint8_t check;
    uint8_t mod = 0;
    //求和
    for(int i = 0; i < len; i++) {
        result = result + ch[i];
    }
    //取模
    mod = result % 256;
    
    //取反
    result = 0xff - mod;
    //加1
    check = result % 256;
    check += 1;
    return check;
}

weightSensor_ZZH_201::weightSensor_ZZH_201(void)
{
    mStatus = false;
    mIsCalib = false;

    pthread_cond_init(&mCond, NULL);
    pthread_mutex_init(&mLock, NULL);
}
weightSensor_ZZH_201::~weightSensor_ZZH_201()
{
	pthread_mutex_destroy(&mLock);
    pthread_cond_destroy(&mCond);
}
int32_t weightSensor_ZZH_201::onServerConnected(void)
{
    start();
    return 0;
}

int32_t weightSensor_ZZH_201::onServerDisconnected(void)
{
    stop();
    return 0;
}

int32_t weightSensor_ZZH_201::onDataRecevied(const char *p, uint32_t len)
{
	logd("len %d, recv  %s", len, my::hex(my::constr(p, len)).c_str());

    onCmdData(mRcvArray, p, len);
    return 0;
}
int32_t weightSensor_ZZH_201::onCmdData(vector<uint8_t> & recvArray, const char *p, uint32_t len)
{
    recvArray.insert(recvArray.end(), p, p + len);

    int32_t parsed = 0;
    do {
        parsed = onDataParse(recvArray.data(), recvArray.size());
        //assert(parsed <= recvArray.size());
        if (recvArray.size() >= (1<<20)) {
            parsed = recvArray.size();
            logd("frame too long!!\n");
        }
        
        if (parsed < 0) {
            recvArray.erase(recvArray.begin(), recvArray.begin() + recvArray.size());
            parsed = 0;
            //logd("frame no header, erase all!!\n");
        } else if (parsed > 0) {
            recvArray.erase(recvArray.begin(), recvArray.begin() + parsed);
            //logd("erase size %d\n", parsed);
        } else {
            logd("alertor frame not complete!!\n");
        }

        if(recvArray.size() <=0 || parsed <= 0) {
            //logd("break");
            break;
        }

    } while(1);

    return parsed;

}


int32_t weightSensor_ZZH_201::onDataParse(const uint8_t *p, uint32_t size)
{
    int32_t start = -1;
    int32_t end = -1;
    uint32_t offset = 0;
    if (size < 14) {
        return -1;
    }
    
    for (int i = 0; i < static_cast<int32_t>(size); ++i) {
        if (p[i] == 'H' && p[i + 1] == 'B' && p[i + 2] == 'T' && p[i + 3] == 'Q') {
            start = i + 4;
        }
        if (p[i] == '\r' && p[i + 1] == '\n') {
            end = i - 1;
        }
    }
 
    if (start > 0 && end > 0) {
        if (onDataValidate(p, size)) {
            onMessage(p, size);
        } else {
            logd("data invaild");
            return -1;
        }
    } else {
        //logd("******start =  %d, end = %d\n", start, end);
        return -1;
    }
    
    return end + 2;
}


bool weightSensor_ZZH_201::onMessage(const uint8_t *p, uint32_t len)
{
    uint16_t unit;
    uint32_t weight = 0;
    uint32_t weightAD = 0;
    uint8_t result1 = 0;
    uint8_t result2 = 0;
    uint8_t result3 = 0;
    uint8_t result4 = 0;

    
    logd("cmd %c%c%c, ID %c%c%c, data %c%c%c%c", p[4], p[5], p[6], p[7], p[8], p[9], p[12], p[13], p[14], p[15]);
    if (p[4] == 'D'&& p[5] == '0' && p[6] == '1') { //解析AD值
        if (len < 20) {
            return false;
        } else {
            if (p[12] >= 48 && p[12] <= 57) {
                result1 = p[12] - 48;
            } else if (p[12] >= 65 && p[12] <= 90) {
                result1 = p[12] - 65 + 10;
            }

            if (p[13] >= 48 && p[13] <= 57) {
                result2 = p[13] - 48;
            } else if (p[13] >= 65 && p[13] <= 90) {
                result2 = p[13] - 65 + 10;
            }
            
            if (p[14] >= 48 && p[14] <= 57) {
                result3 = p[14] - 48;
            } else if (p[14] >= 65 && p[14] <= 90) {
                result3 = p[14] - 65 + 10;
            }

            if (p[15] >= 48 && p[15] <= 57) {
                result4 = p[15] - 48;
            } else if (p[15] >= 65 && p[15] <= 90) {
                result4 = p[15] - 65 + 10;
            }

            weightAD = result1 << 12 | result2 << 8 | result3 << 4 | result4;
			mWeightAD = weightAD;
            pthread_mutex_lock(&mLock);
            pthread_cond_signal(&mCond);
            pthread_mutex_unlock(&mLock);
       }
    }else if (p[4] == 'D'&& p[5] == '8' && p[6] == '1') { //解析载重值
		if (len < 20) {
            return false;
        } else {
            if (p[12] >= 48 && p[12] <= 57) {
                result1 = p[12] - 48;
            } else if (p[12] >= 65 && p[12] <= 90) {
                result1 = p[12] - 65 + 10;
            }

            if (p[13] >= 48 && p[13] <= 57) {
                result2 = p[13] - 48;
            } else if (p[13] >= 65 && p[13] <= 90) {
                result2 = p[13] - 65 + 10;
            }
            
            if (p[14] >= 48 && p[14] <= 57) {
                result3 = p[14] - 48;
            } else if (p[14] >= 65 && p[14] <= 90) {
                result3 = p[14] - 65 + 10;
            }

            if (p[15] >= 48 && p[15] <= 57) {
                result4 = p[15] - 48;
            } else if (p[15] >= 65 && p[15] <= 90) {
                result4 = p[15] - 65 + 10;
            }

            weight = result1 << 12 | result2 << 8 | result3 << 4 | result4;
			mWeight = weight;
			pthread_mutex_lock(&mLock);
            pthread_cond_signal(&mCond);
            pthread_mutex_unlock(&mLock);
		}
    }else if (p[4] == 'D'&& p[5] == '9' && p[6] == '1') {
        if (p[10] == 'O' && p[11] == 'K') {
            mIsCalib = true;
        } else {
            mIsCalib = false;
        }
    	pthread_mutex_lock(&mLock);
        pthread_cond_signal(&mCond);
        pthread_mutex_unlock(&mLock);
    }

    return true;
}

bool weightSensor_ZZH_201::runCmd(int argc, char **argv, string &ack)
{
    logd("argc = %d", argc);
    
    for(int i = 0; i < argc; i++)
    {
		logd("%s ", argv[i]);
    }
	if (argc != 1)
	{
		ack = "arg error";
		return false;
	}
	if (!strcmp(argv[0], "help"))
	{
		ack = "\n cmd getAD\n cmd getWeight\n cmd setCalib\n cmd getCalib\n cmd getStatus\n";
		return true;
	} 
	else if (!strcmp(argv[0], "getAD"))
	{
		sendADCmd();
		char buf[32] = {0};
		sprintf(buf, "ad is %d", mWeightAD);
		ack = buf;
		return true;
	}
	else if (!strcmp(argv[0], "getWeight"))
	{
		sendWeightCmd();
		char buf[32] = {0};
		sprintf(buf, "weight is %d", mWeight);
		ack = buf;
		return true;
	}else if (!strcmp(argv[0], "setCalib")) {
        mIsCalib = false;
	    mCalibData.clear();
        parseCalibDataFromJson();
        dumpCalibData();
        sendCalibCmd();
        if (mIsCalib) {
            ack = "setCalib ok";
            propertySet(PROP_PERSIST_MINIEYE_WEIGHT_CALIB, "true");
        } else {
            ack = "setCalib failed";
            propertySet(PROP_PERSIST_MINIEYE_WEIGHT_CALIB, "false");
        }
    } else if (!strcmp(argv[0], "getCalib")) {
        bool ret = false;
        char propValue[PROP_VALUE_MAX] = {0};
        if (__system_property_get(PROP_PERSIST_MINIEYE_WEIGHT_CALIB, propValue)) {
            ret = !strcmp(propValue, "true");
        }
        if (ret) {
            ack = "already Calib";
        } else {
            ack = "not Calib";
        }
    } else if (!strcmp(argv[0], "getStatus")) {
        if (mStatus) {
            ack = "connect";
        } else {
            ack = "disconnect";
        }
    }
    return true;
}

void weightSensor_ZZH_201::run()
{
    uint32_t noAckCount = 0;
    bool ret = false;
    while(!exiting())
    {

        if (noAckCount > 5) {
            noAckCount = 0;
            mStatus = false;
            sendFlowMessage();
        }
        //AD值
        ret = sendADCmd();
        if (ret) {
            noAckCount = 0;
            mStatus = true;
        } else {
            noAckCount++;
            msleep(1000);
            continue;
        }
        
        msleep(1000);//消息发送间隔至少100ms
        
        //实际载重值
        ret = sendWeightCmd();
        if (ret) {
            noAckCount = 0;
            mStatus = true;
        } else {
            noAckCount++;
            msleep(1000);
            continue;
        }

        sendFlowMessage();
        sleep(5);
    }

}
bool weightSensor_ZZH_201::onDataValidate(const uint8_t *p, uint32_t len)
{
    uint8_t check1 = 0;
    uint8_t check2 = 0;
    uint8_t sum = calSUM(p, len);
    if (p[len - 4] >= 48 && p[len - 4] <= 57) {
        check1 = p[len - 4] - 48;
    } else if (p[len - 4] >= 65 && p[len - 4] <= 90) {
        check1 = p[len - 4] - 65 + 10;
    }
    
    if (p[len - 3] >= 48 && p[len - 3] <= 57) {
        check2 = p[len - 3] - 48;
    } else if (p[len - 3] >= 65 && p[len - 3] <= 90) {
        check2 = p[len - 3] - 65 + 10;
    }
    if (check1 == sum / 16 && check2 == sum % 16) {
        return true;
    } else {
        return false;
    }
    return true;
}
uint8_t weightSensor_ZZH_201::calSUM(const uint8_t *ch, uint32_t len)
{
    uint32_t sum = 0;
    for (int i = 0; i < len - 4; i++) {
        sum += ch[i];
    }
    uint8_t ret = 0xff & sum;
    return ret;
}

bool weightSensor_ZZH_201::sendADCmd(void)
{
	bool ret = false;
    int retcode = 0;

	const char* send = "HBTQD0100064\r\n";
    msgEnque((void*)send, strlen(send));

    
    ret = wait_ack();


    return ret;
}

bool weightSensor_ZZH_201::sendWeightCmd(void)
{
	bool ret = false;
    int retcode = 0;

	const char* send = "HBTQD810006C\r\n";
    msgEnque((void*)send, strlen(send));

    ret = wait_ack();

    return ret;
}

//calib.json重载重单位为T
bool weightSensor_ZZH_201::sendCalibCmd(void)
{
	bool ret = false;
    uint8_t check1 = 0;
    uint8_t check2 = 0;

    char buf[25] = {0};
    std::string send = "HBTQD91000";

    sprintf(buf, "%02x", (uint16_t)mCalibData.size());
    send += buf;
    for(std::map <int, int> ::iterator iter = mCalibData.begin(); iter != mCalibData.end(); iter++) {
        sprintf(buf, "%04x%04x", iter->first, iter->second);
        send += buf;
    }

    
    int32_t len = send.length();
    int32_t sum = 0;
    
    //求和
    for (int32_t i = 0; i < len; i++) {
        sum += send[i];
    }
    uint8_t check = sum & 0xff;

    //计算
    check1 = check / 16;
    check2 = check % 16;
    
    if (check1 >= 0 && check1 <= 9) {
        check1 = 48 + check1;
    } else if (check1 >= 0xa && check1 <= 0xf) {
        check1 = check1 + 65 - 10;
    }

    if (check2 >= 0 && check2 <= 9) {
        check2 = 48 + check2;
    } else if (check2 >= 0xa && check2 <= 0xf) {
        check2 = check2 + 65 - 10;
    }
    
    //填充
    send[len] = check1;
    send[len + 1] = check2;
    
    send[len + 2] = '\r';
    send[len + 3] = '\n';
    
    msgEnque((void*)send.c_str(), len + 4);
    ret = wait_ack();
    return ret;
}

bool weightSensor_ZZH_201::wait_ack(void)
{
    struct timeval now;
    struct timespec timeout;
    int retcode = 0;

    pthread_mutex_lock(&mLock);
    
    gettimeofday(&now, NULL);
    timeout.tv_sec = now.tv_sec + 1;
    timeout.tv_nsec = now.tv_usec * 1000;
    retcode = pthread_cond_timedwait(&mCond, &mLock, &timeout);

    pthread_mutex_unlock(&mLock);
    if (retcode == ETIMEDOUT) {
        /* timeout occurred */
        return false;
    } else {
        /* operate on x and y */
        return true;
    }
    return true;
}

bool weightSensor_ZZH_201::parseCalibDataFromJson(void)
{
    rapidjson::Document doc;
    uint32_t num = 0;
    double ad;
    double load;
    char name[20] = {0};
    RAPIDJSON_LOAD(CALIB_JSON_PATH);

    RAPIDJSON_GET_JSON_STRING(doc, "sensorType", mSensorType);
    RAPIDJSON_GET_JSON_INT(doc, "data_num", num);
    for (int i = 0; i < num; i++) {
        sprintf(name, "calib%d", i);
        RAPIDJSON_GET_JSON_DOUBLE(doc[name], "AD_value", ad);
        RAPIDJSON_GET_JSON_DOUBLE(doc[name], "load_value", load);
        mCalibData.insert(std::pair<int, int>(ad, load*100));
    }
    return true;
}

bool weightSensor_ZZH_201::dumpCalibData(void)
{
    int index = 0;
    logd("sensorType:%s", mSensorType.c_str());
    for(std::map <int, int> ::iterator iter = mCalibData.begin(); iter != mCalibData.end(); iter++)
    {
        logd("%d:%d", iter->first, iter->second);
    }
    return true;
}


bool weightSensor_ZZH_201::sendFlowMessage(void)
{
    expand::weightSensorMessage message;
    message.mUtcTime = my::timestamp::utc_milliseconds();
    message.mUnit = 2;  /* 单位10kg */
    message.mWeight = mWeight;
    message.mWeightAD = mWeightAD;
    message.mStatus = mStatus;
    message.mIsCalib = mIsCalib;

    logd("weight=%d uint=%d", message.mWeight, message.mUnit);
    msgpack::sbuffer sbuf;
    msgpack::pack(sbuf, message);
    ExpandSet::getInstance().sendLibFlow(WEIGHTSENSOR_LIBFLOW_TOPIIC, sbuf.data(), sbuf.size());
    return true;
}

weightSensor_ZHF03::weightSensor_ZHF03() {
    bool ret = false;
    char propValue[PROP_VALUE_MAX] = {0};
    mCanMode = "silent";
    if (__system_property_get(PROP_PERSIST_MINIEYE_WEIGHT_CALIB, propValue)) {
        ret = !strcmp(propValue, "true");
    }
    if (ret) {
        mIsCalib = true;
    } else {
        mIsCalib = false;
    }
    memset(mRaw, 0, 8);
}

void weightSensor_ZHF03::setCanMode(std::string mode) {
    if (true == mCanInfoInit
        && mode != mCanMode 
        && true == mCanEnable) {
        if (mode == "normal") {
            #if 0
            /* 清除过滤以便接收校正信息 */
            clearFilter();
            #endif
            /* 进入普通模式保证能正常发送 */
            DeviceHub::getInstance().setCanMode(DeviceHub::DEVICE_TYPE_CAN0, mCanBaudRate, mode);            
        } else {
            /* 进入普通模式保证能正常发送 */
            DeviceHub::getInstance().setCanMode(DeviceHub::DEVICE_TYPE_CAN1, mCanBaudRate, mode);
        }
        mCanMode = mode;
    }
}

void weightSensor_ZHF03::clearFilter() {
    if (0 == mCanChId) {
        DeviceHub::getInstance().setCanFilter(DeviceHub::DEVICE_TYPE_CAN0, 0);
    } else if (1 == mCanChId) {
        DeviceHub::getInstance().setCanFilter(DeviceHub::DEVICE_TYPE_CAN0, 0);
    }
}

void weightSensor_ZHF03::setFilter(uint32_t canId) {
    if (0 == mCanChId) {
        DeviceHub::getInstance().setCanFilter(DeviceHub::DEVICE_TYPE_CAN0, canId);
    } else if (1 == mCanChId) {
        DeviceHub::getInstance().setCanFilter(DeviceHub::DEVICE_TYPE_CAN0, canId);
    }
}

bool weightSensor_ZHF03::setLoadSensor(const CORRECT_STEP loop) {
    logd("send to load sensor loop:%d!\n", loop);    
    canFrame_t canData;
    #if 1
    canData.frameId = mCalibParam.canCalibReqFrameId;  /* 载重传感器校正报文canid */
    canData.len = 8;
    memset(canData.data, 0, 8);
    switch(loop) {
        case CORECT_STEP_TORQUE:{ 
            uint16_t torque = mCalibParam.torque;
            logd("torque:%x!\n", torque);
            canData.data[0] = 0x01;            
            canData.data[1] = torque & 0xff;
            canData.data[2] = torque >> 8;
            break;
        }
        case CORECT_STEP_ORIENTATION:{
            uint8_t orientation = mCalibParam.orientation;
            logd("orientation:%c!\n", orientation);
            canData.data[0] = 0x03;
            canData.data[1] = orientation;
            break;
        }
        case CORECT_STEP_ACCELERATED_FILTER:{
            uint16_t acceleratedFilter = mCalibParam.acceleratedFilter;
            uint16_t impactFilter = mCalibParam.impactFilter;
            logd("acceleratedFilter:%x,impactFilter:%x!\n", acceleratedFilter, impactFilter);
            canData.data[0] = 0x07;
            canData.data[1] = acceleratedFilter & 0xff;
            canData.data[2] = acceleratedFilter >> 8;
            canData.data[3] = impactFilter & 0xff;
            canData.data[4] = impactFilter >> 8;
            break;
        }
        case CORECT_STEP_ACCELERATED_INITIAL:{
            uint16_t acceleratedInitial = mCalibParam.acceleratedInitial;
            logd("acceleratedInitial:%x!\n", acceleratedInitial);
            canData.data[0] = 0x05;
            canData.data[1] = acceleratedInitial & 0xff;
            canData.data[2] = acceleratedInitial >> 8;
            break;
        }        
        case CORECT_STEP_LOAD_LIMIT:{
            /* 目前仅设置满载值 */
            uint16_t fullLoadLimit = mCalibParam.fullLoadLimit;
            logd("fullLoadLimit:%x!\n", fullLoadLimit);
            canData.data[0] = 0x04;
            canData.data[1] = 0x01; /* 0x01 满载载重值；0x02 半载载重值；0x03 空载载重值；  */
            canData.data[2] = fullLoadLimit & 0xff;
            canData.data[3] = fullLoadLimit >> 8;
            break;
        }
        case CORECT_STEP_LOAD_CALIBRATION_COEFFICIENT:{
            /* 目前仅设置满载标定系数 */
            uint8_t fullLoadLimitCoefficient = mCalibParam.fullLoadLimitCoefficient;
            logd("fullLoadLimitCoefficient:%x!\n", fullLoadLimitCoefficient);            
            canData.data[0] = 0x08;
            canData.data[1] = 0x03; /* 0x01 空载标定系数；0x02 半载标定系数;0x03 满载标定系数； */
            canData.data[2] = fullLoadLimitCoefficient;
            break;
        }
        case CORECT_STEP_TRANSMISSION_TYPE:{
            uint8_t transmissionType = mCalibParam.transmissionType;
            logd("transmissionType:%c!\n", transmissionType);
            canData.data[0] = 0x14;
            canData.data[1] = transmissionType;
            break;
        }
        case CORECT_STEP_TRANSMISSION_STATUS:{
            uint8_t transmissionStatus = mCalibParam.transmissionStatus;
            logd("transmissionStatus:%c!\n", transmissionStatus);
            canData.data[0] = 0x19;
            canData.data[1] = transmissionStatus;
            break;
        }
        case CORECT_STEP_SAVE:{
            canData.data[0] = 0xF0;
            canData.data[1] = 0x32;
            break;     
        }       
        case CORECT_STEP_END:
        default:    {
            break;
        }
    }
    //Manager::getInstance().sendMcuMsg(MCU_MSG_TYPE_SEND_TO_CAN0, (uint8_t*)&canData, sizeof(canData));
    #if 0
    uint8_t buf[MCU_MSG_MAX_SIZE];
	McuMessage *pMsg = (McuMessage *)&buf[0];
	memset(buf, 0, sizeof(buf));
	pMsg->type = cmd;
	pMsg->len = len;
    if (data && len) {
        memcpy(&pMsg->u.u8Array[0], data, len);
    }
    #endif
    msgEnque((void*)&canData, sizeof(canData));
    
    logd("correct load send %d cmd,msg:%x %x %x %x %x %x %x %x!\n", loop, 
            canData.data[0], canData.data[1], canData.data[2], canData.data[3],
            canData.data[4], canData.data[5], canData.data[6], canData.data[7]);
    #endif
    return true;
}

bool weightSensor_ZHF03::correctLoadSensor() {
    if (!mIsCalib && true == mCanInfoInit) {
        if (mCanMode != "normal") {
            // 进入normal 模式接收传感器校正结果，发送校正参数
            setCanMode("normal");
            setFilter(mCalibParam.canCalibRspFrameId);
        }
        if (CORECT_STEP_END == mLoadCorrectLoop.correctStep) {
            /* 校正完成 */
            mIsCalib = true;
            logd("载重传感器校准完成");
            propertySet(PROP_PERSIST_MINIEYE_WEIGHT_CALIB, "true");
            /* 进入silent模式 */    
            setCanMode("silent");
            setFilter(mCalibParam.canLoadFrameId);
            return true;
        }

        if (MSG_STEP_VALID == mLoadCorrectLoop.msgStep) {
            if (setLoadSensor(mLoadCorrectLoop.correctStep)) {
                logd("send to load sensor success!\n");
                mLoadCorrectLoop.msgStep = MSG_STEP_SEND;
                mMsgTm = my::timestamp::now();
            } else {
                logd("send to load sensor failed!\n");
            }
        } else if (MSG_STEP_SEND == mLoadCorrectLoop.msgStep) {
            /* 5s 超时重发 */
            if (mMsgTm.elapsed() > 5000) {
                mLoadCorrectLoop.msgStep = MSG_STEP_VALID;
            }            
        } else if (MSG_STEP_BACK == mLoadCorrectLoop.msgStep) {
            mLoadCorrectLoop.msgStep = MSG_STEP_END;
        } else if (MSG_STEP_END == mLoadCorrectLoop.msgStep) {
            mLoadCorrectLoop.msgStep = MSG_STEP_VALID;
            mLoadCorrectLoop.nextStep();
        }
    }
    return true;
}

void weightSensor_ZHF03::run()
{
    uint32_t noAckCount = 0;
    bool ret = false;
    while(!exiting()) {
        /* 获取系统信息 */
        {
            bool initFlg = false;
            {
                std::lock_guard<std::mutex> lock(mLock);
                initFlg = mSysInfoInit;
            }
            if (!initFlg) {
                if (mGetSysInfo.elapsed() > 60000) {
                    /* 1min未获取到系统信息重新请求一次 */
                    reqSysInfo();
                    std::lock_guard<std::mutex> lock(mLock);
                    mSysInfo.reset();
                }
                usleep(500000);
                continue;
            }            
        }
    
        /* 传感器校正 */
        correctLoadSensor();

        /* 发送载重值 */
        sendFlowMessage();
        usleep(500000);
    }    
}

//#define TEST_WEIGHT
bool weightSensor_ZHF03::sendFlowMessage(void)
{
    #ifdef TEST_WEIGHT
    mWeight = 4200;
    mStatus = 1;
    mRspTorque = mCalibParam.torque;
    mVersion = 100;
    mRaw[0] = 0;
    mRaw[1] = (mWeight / 10) & 0xff;
    mRaw[2] = ((mWeight / 10) >> 8) & 0xff;
    mRaw[3] = 88;
    mRaw[4] = 1;
    mRaw[5] = mRspTorque & 0xff;
    mRaw[6] = (mRspTorque >> 8) & 0xff;
    mRaw[7] = mVersion;
    #endif    
    if (mWeight > 0) {
        expand::weightSensorZHF03Message message;
        message.mUtcTime = my::timestamp::utc_milliseconds();
        message.mUnit = 2;
        message.mWeight = mWeight;
        message.mStatus = mStatus;
        message.mIsCalib = mIsCalib;
        message.mTorque = mRspTorque;
        message.mVersion = mVersion;
        for(int i = 0; i < sizeof(mRaw); i++) {
            message.mRaw.push_back(mRaw[i]);
        }
        //logd("weight=%d uint=%d", message.mWeight, message.mUnit);
        msgpack::sbuffer sbuf;
        msgpack::pack(sbuf, message);
        ExpandSet::getInstance().sendLibFlow(WEIGHTSENSORZHF03_LIBFLOW_TOPIIC, sbuf.data(), sbuf.size());
    }
    return true;
}

void weightSensor_ZHF03::correctResponseParse(const uint8_t * canData) 
{
    uint8_t tmp[8] = {0};
    memcpy(tmp, canData, 8);
    switch(tmp[2]){
        case 0x01:{ 
            if (0x5a == tmp[3]) {
                logd("set success!\n");
                mLoadCorrectLoop.msgStep = MSG_STEP_BACK;
            } else {
                logd("set failed!\n");
                mLoadCorrectLoop.msgStep = MSG_STEP_VALID;
            }
            break;
        }
        case 0x03:{
            if (0x5a == tmp[3]) {
                logd("set success!\n");
                mLoadCorrectLoop.msgStep = MSG_STEP_BACK;
            } else {
                logd("set failed!\n");
                mLoadCorrectLoop.msgStep = MSG_STEP_VALID;
            }
            break;
        }
        case 0x07:{
            if (0x5a == tmp[3]) {
                logd("set success!\n");
                mLoadCorrectLoop.msgStep = MSG_STEP_BACK;
            } else {
                logd("set failed!\n");
                mLoadCorrectLoop.msgStep = MSG_STEP_VALID;
            }
            break;
        }
        case 0x05:{
            if (0x5a == tmp[3]) {
                logd("set success!\n");
                mLoadCorrectLoop.msgStep = MSG_STEP_BACK;
            } else {
                logd("set failed!\n");
                mLoadCorrectLoop.msgStep = MSG_STEP_VALID;
            }
            break;
        }        
        case 0x04:{
            if (0x5a == tmp[3]) {
                logd("set success!\n");
                mLoadCorrectLoop.msgStep = MSG_STEP_BACK;
            } else {
                logd("set failed!\n");
                if (0x1 == tmp[4]) {
                    logd("满载载重值设置失败");
                } else if (0x2 == tmp[4]) {
                    logd("半载载重值设置失败");
                } else if (0x3 == tmp[4]) {
                    logd("空载载重值设置失败");
                }
                mLoadCorrectLoop.msgStep = MSG_STEP_VALID;
            }
            break;
        }
        case 0x08:{
            if (0x5a == tmp[3]) {
                logd("set success!\n");
                mLoadCorrectLoop.msgStep = MSG_STEP_BACK;
            } else {
                logd("set failed!\n");
                if (0x1 == tmp[4]) {
                    logd("满载载重标定系数设置失败");
                } else if (0x2 == tmp[4]) {
                    logd("半载载重标定系数设置失败");
                } else if (0x3 == tmp[4]) {
                    logd("空载载重标定系数设置失败");
                }
                mLoadCorrectLoop.msgStep = MSG_STEP_VALID;
            }
            break;
        }
        case 0x14:{
            if (0x5a == tmp[3]) {
                logd("set success!\n");
                mLoadCorrectLoop.msgStep = MSG_STEP_BACK;
            } else {
                logd("set failed!\n");                  
                mLoadCorrectLoop.msgStep = MSG_STEP_VALID;
            }
            break;
        }
        case 0x19:{
            if (0x5a == tmp[3]) {
                logd("set success!\n");
                mLoadCorrectLoop.msgStep = MSG_STEP_BACK;
            } else {
                logd("set failed!\n");
                mLoadCorrectLoop.msgStep = MSG_STEP_VALID;
            }
            break;
        }
        case 0xF0:{
            if (0x5a == tmp[3]) {
                logd("set success!\n");
                mLoadCorrectLoop.msgStep = MSG_STEP_BACK;
            } else {
                logd("set failed!\n");
                mLoadCorrectLoop.msgStep = MSG_STEP_VALID;
            }
            break;     
        } 
    }
}

int weightSensor_ZHF03::canDataDeal(const uint32_t canID, const uint8_t * canData){
    //logd("canID:0x%x\n", canID);
    if (0 == mWorkMode) {
        if (mCalibParam.canSysInfoRspFrameId == canID) {
            /* 系统信息 */
            if (parseSysInfo(canData)) {
                return true;
            }
        }
    }
    
    if (mCalibParam.canCalibRspFrameId == canID) {
        /* 校正信息 */
        correctResponseParse(canData);
    }else if (mCalibParam.canLoadFrameId == canID){
        /* 载重信息 */
        if (0 == canData[0]){
            /* 载重值结果正常 */            
            memcpy(mRaw, &canData, sizeof(mRaw));
            int currentCapacity = canData[2];
            currentCapacity = (currentCapacity << 8) | canData[1];
            mWeight = currentCapacity * 10; /* 单位为10kg，载重传感器上传的重量单位为100kg */
            mStatus = canData[4];
            mRspTorque = canData[6];
            mRspTorque = (mRspTorque << 8) | canData[5];
            mVersion = canData[7];
            logd("[xyj]weight %f,mStatus:%d, mRspTorque:%d, mVersion:%d T!\n", 
                    mWeight / 100.0, mStatus, mRspTorque, mVersion);
        } else if (0x1 == canData[0]) {
            if (mLoadErrorTm.elapsed() > 5000) {
                logd("加速度计失效");
            }  
            
        } else if (0x2 == canData[0]) {
            if (mLoadErrorTm.elapsed() > 5000) {
                logd("缺失can报文");
            }
        } else if (0x4 == canData[0]) {
            if (mLoadErrorTm.elapsed() > 5000) {
                logd("缺失ccvs离合器信号");
            }
        } else {
            logd("load msg %02x %02x %02x %02x %02x %02x %02x %02x!\n", 
                        canData[0], canData[1],canData[2],canData[3],
                        canData[4],canData[5],canData[6], canData[7]);
        }
        
    }
    return true;
}

bool weightSensor_ZHF03::parseSysInfo(const uint8_t * data) {
    my::string canData;

#if 0    
    for (int i = 0; i < 8; i++) {
        canData.appendf(" %x ", data[i]);
    }
    loge("%s,start:%d\n", canData.c_str(), mSysInfo.start);
#endif    
    if (!mSysInfo.start) {
        if (0x20 == data[0] 
            && 0x02 == data[1] 
            && 0 == data[3]
            && 0 == data[5]
            && 0x02 == data[6]
            && 0x20 == data[7]) {
            std::lock_guard<std::mutex> lock(mLock);
            mSysInfo.frameNum = data[2];
            mSysInfo.dataBits = data[4];
            mSysInfo.start = true;
        } else {
            /* 异常报文 */
            loge("error\n");
            return false;
        }
    } else {
        /* 接收报文 tmp[2]为报文ID，累计增加*/
        if (0x20 != data[0] || 0x02 != data[1] || (mSysInfo.lastFrameId + 1) != data[2]) {
            /* 异常报文 */
            loge("invalid frame!\n");
            return false;
        }   
        std::lock_guard<std::mutex> lock(mLock);
        mSysInfo.lastFrameId  = data[2];
        for (int i = 3; i < 8; i++) {
            if (data[i] == 0 && mSysInfo.sysInfoData.length() >= mSysInfo.dataBits) {
                break;
            } else {
                mSysInfo.sysInfoData << data[i];
            }
        }
        //loge("len:%d,dataBits:%d!\n", mSysInfo.sysInfoData.length(), mSysInfo.dataBits);
        mSysInfo.recNum++;
        if (mSysInfo.recNum == mSysInfo.frameNum) {
            if (mSysInfo.sysInfoData.length() == mSysInfo.dataBits) {
                /* 数据完整获取系统信息结束 */
                /* 按照川标协议重整信息 */
#if 1            
/* 中寰对id长度及内容加密，待确实是否需要额外添加id字段长度 */
                my::string pre;
                my::string sub;
                pre.append(mSysInfo.sysInfoData, 0, mSysInfo.sysInfoData.length() - 19);
                sub.append(mSysInfo.sysInfoData, mSysInfo.sysInfoData.length() - 19, 19);
                mSysInfo.sysInfoData = pre;
                mSysInfo.sysInfoData << (char)16; /* ID字段长度 */
                mSysInfo.sysInfoData += sub;
#endif
                mSysInfoInit = true;
                mWorkMode  = 1;
                comeInWorkMode();
                my::hexdump(mSysInfo.sysInfoData, true);
            } else {
                loge("len:%d,dataBits:%d!\n", mSysInfo.sysInfoData.length(), mSysInfo.dataBits);
            }
        }
    }

    return true;    
}

int32_t weightSensor_ZHF03::onDataParse(const uint8_t *p, uint32_t size)
{
    if (sizeof(canFrame_t) > size) {
        return -1;
    }

    int msgLen = 0;
    canFrame_t *pMsg = (canFrame_t *)p;
    if (sizeof(canFrame_t) > size) {
        logd("size:%d, canFrame_t:%d!\n", size, sizeof(canFrame_t));
        return -1;
    }
#if 0
    logd("onDataParse, canId:%x, canData: %0x-%0x-%0x-%0x-%0x-%0x-%0x-%0x\n", 
        pMsg->frameId, pMsg->data[0], pMsg->data[1], pMsg->data[2], 
        pMsg->data[3], pMsg->data[4], pMsg->data[5], pMsg->data[6], pMsg->data[7]);
#endif
    canDataDeal(pMsg->frameId, pMsg->data);

    return sizeof(canFrame_t);
}

int32_t weightSensor_ZHF03::onCmdData(vector<uint8_t> & recvArray, const char *p, uint32_t len)
{
    recvArray.insert(recvArray.end(), p, p + len);
    int32_t parsed = 0;
    do {
        parsed = onDataParse(recvArray.data(), recvArray.size());
        if (recvArray.size() >= (1<<20)) {
            parsed = recvArray.size();
            logd("frame too long!!\n");
        }
        
        if (parsed < 0) {
            recvArray.erase(recvArray.begin(), recvArray.begin() + recvArray.size());
            parsed = 0;
            //logd("frame no header, erase all!!\n");
        } else if (parsed > 0) {
            recvArray.erase(recvArray.begin(), recvArray.begin() + parsed);
            //logd("erase size %d\n", parsed);
        } else {
            logd("alertor frame not complete!!\n");
        }

        if(recvArray.size() <=0 || parsed <= 0) {
            //logd("break");
            break;
        }

    } while(1);

    return parsed;

}

int32_t weightSensor_ZHF03::onServerConnected(void)
{
    start();
    
    /* 开机获取系统信息 */
    comeInGetSysInfo();
    
    return 0;
}

int32_t weightSensor_ZHF03::comeInWorkMode()
{
    setCanMode("silent");
    setFilter(mCalibParam.canLoadFrameId);

    reqSysInfo();
    return 0;
}

bool weightSensor_ZHF03::reqSysInfo()
{
    /* 请求系统信息 */
    canFrame_t canData;
    canData.frameId = mCalibParam.canSysInfoReqFrameId;  /* 载重传感器系统信息获取报文canid */
    canData.len = 8;
    memset(canData.data, 0, 8);
    canData.data[0] = 0x20;
    canData.data[1] = 0x02;
    msgEnque((void*)&canData, sizeof(canData));
    loge("req weightSensor_ZHF03 sys info!\n");
    mGetSysInfo = my::timestamp::now();
    return true;
}

int32_t weightSensor_ZHF03::comeInGetSysInfo()
{
    setCanMode("normal");
    setFilter(mCalibParam.canSysInfoRspFrameId);
    return 0;
}


int32_t weightSensor_ZHF03::onServerDisconnected(void)
{
    stop();
    return 0;
}

int32_t weightSensor_ZHF03::onDataRecevied(const char *p, uint32_t len)
{
    //logd("len %d, recv  %s", len, my::hex(my::constr(p, len)).c_str());

    onCmdData(mRcvArray, p, len);
    return 0;
}

void weightSensor_ZHF03::defaultCalibConfig() {
    mCalibParam.torque = 2200; /* 扭矩 200~3000Nm */
    mCalibParam.orientation = 1; /* 1:朝向驾驶室 2:朝向车尾 */
    mCalibParam.acceleratedFilter = 700; /* 加速度系数50~800       */
    mCalibParam.impactFilter = 550;    /* 冲击度系数 50~800      */
    mCalibParam.acceleratedInitial = 500; /* 加速度初值 -1000~1000 发给载重传感器需加上偏移量1000即 -1000发送给载重传感器0（-1000+1000=0）      */
    mCalibParam.fullLoadLimit = 30000; /* 满载值   1000~60000kg  */
    mCalibParam.halfLoadLimit = 0; /* 半载值   1000~60000kg  */
    mCalibParam.emptyLoadLimit = 0; /* 空载值   1000~60000kg  */
    mCalibParam.fullLoadLimitCoefficient = 100; /* 满载标定系数0~200         */
    mCalibParam.halfLoadLimitCoefficient = 0; /* 半载标定系数0~200         */
    mCalibParam.emptyLoadLimitCoefficient = 0; /* 空载标定系数0~200         */
    mCalibParam.transmissionType = 1;         /* 0:手动档    1:自动档 */
    mCalibParam.transmissionStatus = 1;     /* 0:传动系信号0为断开 1:传动系信号1为断开                 */
    mCalibParam.canLoadFrameId = 0x18feeafe;    /* 载重值上报canid */
    mCalibParam.canCalibReqFrameId = 0x18fffefe;    /* 校准请求canid */
    mCalibParam.canCalibRspFrameId = 0x18fefffe;    /* 校准响应canid */

    mCalibParam.canSysInfoReqFrameId = 0x18fffeee;  /* 获取系统信息请求can id */
    mCalibParam.canSysInfoRspFrameId = 0x18fefffe;  /* 系统信息上报can id */
}

bool weightSensor_ZHF03::loadCalibConfig() {
    if (access("/data/minieye/idvr/etc/zhf03calib.json", F_OK)) {
        defaultCalibConfig();
        logd("/data/minieye/idvr/etc/zhf03calib.json not exist!\n");
        return false;
    }
    defaultCalibConfig();
    rapidjson::Document doc;
    RAPIDJSON_LOAD("/data/minieye/idvr/etc/zhf03calib.json");
    int val;
    RAPIDJSON_GET_JSON_INT(doc["calib"], "torque", val);
    mCalibParam.torque = val;
    RAPIDJSON_GET_JSON_INT(doc["calib"], "orientation", val);
    mCalibParam.orientation = val;       
    RAPIDJSON_GET_JSON_INT(doc["calib"], "acceleratedFilter", val);
    mCalibParam.acceleratedFilter = val;
    RAPIDJSON_GET_JSON_INT(doc["calib"], "impactFilter", val);
    mCalibParam.impactFilter = val;
    RAPIDJSON_GET_JSON_INT(doc["calib"], "acceleratedInitial", val);
    mCalibParam.acceleratedInitial = val;
    RAPIDJSON_GET_JSON_INT(doc["calib"], "fullLoadLimit", val);
    mCalibParam.fullLoadLimit = val;
    RAPIDJSON_GET_JSON_INT(doc["calib"], "halfLoadLimit", val);
    mCalibParam.halfLoadLimit = val;
    RAPIDJSON_GET_JSON_INT(doc["calib"], "emptyLoadLimit", val);
    mCalibParam.emptyLoadLimit = val;
    RAPIDJSON_GET_JSON_INT(doc["calib"], "fullLoadLimitCoefficient", val);
    mCalibParam.fullLoadLimitCoefficient = val;
    RAPIDJSON_GET_JSON_INT(doc["calib"], "halfLoadLimitCoefficient", val);
    mCalibParam.halfLoadLimitCoefficient = val;
    RAPIDJSON_GET_JSON_INT(doc["calib"], "emptyLoadLimitCoefficient", val);
    mCalibParam.emptyLoadLimitCoefficient = val;
    RAPIDJSON_GET_JSON_INT(doc["calib"], "transmissionType", val);
    mCalibParam.transmissionType = val;
    RAPIDJSON_GET_JSON_INT(doc["calib"], "transmissionStatus", val);
    mCalibParam.transmissionStatus = val;
    RAPIDJSON_GET_JSON_INT(doc["canid"], "canLoadFrameId", val);
    mCalibParam.canLoadFrameId = val;
    RAPIDJSON_GET_JSON_INT(doc["canid"], "canCalibReqFrameId", val);
    mCalibParam.canCalibReqFrameId = val;
    RAPIDJSON_GET_JSON_INT(doc["canid"], "canCalibRspFrameId", val);
    mCalibParam.canCalibRspFrameId = val;

    RAPIDJSON_GET_JSON_INT(doc["canid"], "canSysInfoReqFrameId", val);
    mCalibParam.canSysInfoReqFrameId = val;

    RAPIDJSON_GET_JSON_INT(doc["canid"], "canSysInfoRspFrameId", val);
    mCalibParam.canSysInfoRspFrameId = val;


    logd("[xyj]torque:%d,canLoadFrameId:0x%x, canCalibReqFrameId:0x%x, canCalibRspFrameId:0x%x!\n", mCalibParam.torque,
            mCalibParam.canLoadFrameId, mCalibParam.canCalibReqFrameId, mCalibParam.canCalibRspFrameId);
    return true;
}

bool weightSensor_ZHF03::loadConfig() {
    if (access("/data/minieye/idvr/etc/expand.json", F_OK)) {
        return false;
    }
    rapidjson::Document doc;
    RAPIDJSON_LOAD("/data/minieye/idvr/etc/expand.json");
    if (0 == mCanChId) {
        RAPIDJSON_GET_JSON_STRING(doc["CAN1"], "baudrate", mCanBaudRate);
        RAPIDJSON_GET_JSON_BOOL(doc["CAN1"], "enable", mCanEnable);
        RAPIDJSON_GET_JSON_STRING(doc["CAN1"], "mode", mCanMode);
        mCanInfoInit = true;
    } else if (1 == mCanChId){        
        RAPIDJSON_GET_JSON_STRING(doc["CAN2"], "baudrate", mCanBaudRate);
        RAPIDJSON_GET_JSON_BOOL(doc["CAN2"], "enable", mCanEnable);
        RAPIDJSON_GET_JSON_STRING(doc["CAN2"], "mode", mCanMode);
        mCanInfoInit = true;
    }
    
    return true;
}


bool weightSensor_ZHF03::runCmd(int argc, char **argv, string &ack)
{
    logd("argc = %d", argc);
    
    for(int i = 0; i < argc; i++)
    {
        logd("%s ", argv[i]);
    }
    if (argc != 1)
    {
        ack = "arg error";
        return false;
    }
    if (!strcmp(argv[0], "help"))
    {
        ack = "\n cmd getAD\n cmd getWeight\n cmd setCalib\n cmd getCalib\n cmd getStatus\n cmd getSysInfo\n";
        return true;
    }
    else if (!strcmp(argv[0], "getWeight"))
    {
        char buf[32] = {0};
        sprintf(buf, "weight is %d", mWeight);
        ack = buf;
        return true;
    } else if (!strcmp(argv[0], "setCalib")) {
        mIsCalib = false;
        if (!mIsCalib) {
            ack = "setCalib ok";
            propertySet(PROP_PERSIST_MINIEYE_WEIGHT_CALIB, "false");
        }
    } else if (!strcmp(argv[0], "getCalib")) {
        bool ret = false;
        char propValue[PROP_VALUE_MAX] = {0};
        if (__system_property_get(PROP_PERSIST_MINIEYE_WEIGHT_CALIB, propValue)) {
            ret = !strcmp(propValue, "true");
        }
        if (ret) {
            ack = "already Calib";
        } else {
            ack = "not Calib";
        }
    } else if (!strcmp(argv[0], "getStatus")) {
        if (mStatus) {
            ack = "stabilize";
        } else {
            ack = "variation";
        }
    } else if (!strcmp(argv[0], "getSysInfo")) {
        /* 查询系统信息 */
        if (!mSysInfoInit) {
            return false;
        } else {
            ack = "sysInfo:";
            ack += mSysInfo.sysInfoData.c_str();
        }
    }
    return true;
}

} //namespace minieye

