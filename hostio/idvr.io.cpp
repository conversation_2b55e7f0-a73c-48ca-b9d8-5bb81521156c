#include "idvr.io.h"
#include "manager.h"
#include <stdio.h>
#include "idvr.io.gui.h"
#include <sys/system_properties.h>
#include <time.h>
#include <math.h>
#include "idvr.io.gpsmiles.h"
#include "rapidjson.wrap.h"
#include "idvrProperty.h"

using namespace rapidjson;

const double EARTH_RADIUS = 6378.137;       // 地球半径
const double EARTH_RADIUS_C = 6378137;      // 赤道半径
const double EARTH_RADIUS_J = 6356725;      // 极半径
const double PI = 3.141592654;              // PI

double Rad(double d)
{
       return d * PI / 180.0;
}

double get_distance(double la1, double lo1, double la2, double lo2)
{
    double a = Rad(la1) - Rad(la2);
    double b = Rad(lo1) - Rad(lo2);
    double s = 2 * asin(sqrt(pow(sin(a/2), 2) + cos(Rad(la1))*cos(Rad(la2))*pow(sin(b/2),2)));

    s = s * EARTH_RADIUS;
    return s;
}

string uptime(time_t sec)
{
    int day = sec/86400;
    int hour = (sec%86400)/3600;
    int min = ((sec%86400)%3600)/60;
    int second = ((sec%86400)%3600)%60;

    char buf[128];

    snprintf(buf, sizeof(buf), "%d day, %02d:%02d:%02d", day, hour, min, second);
    return buf;
}

static string getTimestamp(time_t *t)
{
    struct tm* timeinfo;
    struct timeval tv;
    char msglog[128];
    char buffer[64];

    memset(buffer, 0, sizeof buffer);

    if (t) {
        timeinfo = localtime(t);
        strftime(buffer, sizeof buffer, "%Y-%m-%d %H:%M:%S", timeinfo);
        return buffer;
    }

    gettimeofday(&tv, NULL);
    timeinfo = localtime(&tv.tv_sec);
    strftime(buffer, sizeof buffer, "%Y-%m-%d %H:%M:%S.", timeinfo);
    snprintf(msglog, sizeof(msglog), "%s%ld", buffer, tv.tv_usec/1000);

    return msglog;
}

const char work_mode_name[WORK_MODE_MAX][32] = {
    "ft",
    "aging",
    "setup",
    "normal",
    "sample",
};

#define BACKLIGHT_TIME_DEFAULT (30)
static string getTimestamp(time_t *t);

extern char HOME[];
SINGLETON_STATIC_INSTANCE(IOMsgHandler);

IOMsgHandler::IOMsgHandler(int baudrate)
    : clk(true)
    , trigger_clk(true)
    , status_log_clk(0)
    , gbt19056(this)
    , func(0)
    , func_last(0)
    , mCan(this->hal_send, this)
    , mDeg_lat(0.0)
    , mDeg_lon(0.0)
    , mMcuInited(false)
    , mMcuVersionOk(false)
    , mICCardidOk(0)
    , mICCardInsertValid(false)
    , mICCardPullOutValid(false)
    , mPwrDn(0)
    , mPwrDnPending(0)
    , mAcc_last(0)
    , mAccStatus(E_ACC_INVAILD)
    , mAccOffEvent(false)
    , mSysOffTime{0,0}
    , mPwrKeyRstDone(false)
    , mSpeed_x10(0)
    , mTurnl(0)
    , mTurnr(0)
    , mPulses_speed_x10(0)
    , mPulses_fre(0)
    , mPulses_turnl(0)
    , mPulses_turnr(0)
    , mCan_speed_x10(0)
    , mCan_turnl(0)
    , mCan_turnr(0)
    , mGpsCalibEnable(true)
    , mLastFreq(0.0f)
    , mPwrDnpdingTime{0,0}
    , mGpsUpdateTime{0,0}
    , mCanMsgUpdateTime{0,0}
    , mHeartBeatTime{0,0}
    , mGpsMsgPass(false)
    , mAdcVoltage{0,0,0,0,0}
    , mIcInsertDone(false)
    , mMode(WORK_MODE_FT)
    , mIcReadRetry(false)
    , mGpsPlayBack(false)
    , mGpsNmeaLog(false)
    , mSocRebootEnable(true)
    , mLcdBackLightTime(BACKLIGHT_TIME_DEFAULT)
    , mCamCh(0x000000FF)
    , mAdisp_mode(ADISP_NORMAL)
    , mSpeedLimit(-1)
{
    char propValue[PROP_VALUE_MAX] = {0};
    if (__system_property_get("persist.hostio.SocRebootEnable", propValue) > 0) {
        if (propValue[0]) {
            mSocRebootEnable = !!atoi(propValue);
        }
    }
    if (__system_property_get("persist.hostio.tank.size", propValue) > 0) {
        if (propValue[0]) {
            mTankSize = atof(propValue);
        }
    }
    stack.state = 0;
    stack.header.len = 0;

    mSys.power_low = 0;
    mSys.power_off = 0;

    mKeyHitEvent = true;
    clock_gettime(CLOCK_MONOTONIC, &mKeyHitTime);

    get_work_mode();

    mLogger = new FileLog("/data/minieye/idvr/log/");
    mLogger->prepare();

    mLogger->mlog("##hostio start, uptime: %s", uptime(mKeyHitTime.tv_sec).c_str());

    mGpsLogger = new FileLog("/data/minieye/idvr/gps_log/", 5 * 1024 * 1024, 10);
    mGpsLogger->prepare();

    mNmeaLogger = new FileLog("/data/minieye/idvr/nmea/", 50 * 1024 * 1024, 10);
    mNmeaLogger->prepare();


    memset(mLogVerbose, 0, LOG_TYPE_MAX);
	vehicle.state = VEHICLE_STOP;

    mcuAgent = new IpcServer(SH_NAME_HOSTIO);
    mQueue = new MsgQueue(this, baudrate);

    McuHdlF= new McuAgentHdlFast(this);
    McuHdlF->start();

    pMonitor = new MonitorThead(this);
    pMonitor->start();

    nmea_zero_INFO(&mInfo);
    nmea_parser_init(&mParser);
#if 0    
    get_last_gps_location();
#endif    

    get_local_prop();
    setDevId();

    if (mNMEADataEnable & 1)
    {
        mLibflowServer = new LibflowServer("0.0.0.0", "23888", "MINIEYE.NMEA");
        mLibflowServer->start();
    }

    if (mNMEADataEnable & 2) {
        mRbGNSS = new CRingBuf("hostio", "GNSS", (1<<20), CRB_PERSONALITY_WRITER, true);
    }

    memset(propValue, 0, sizeof(propValue));
    if (__system_property_get(PROP_PERSIST_MINIEYE_GPS_PDOP_THRES, propValue) > 0) {
        mPDOPThres = atof(propValue);
    } else {
        mPDOPThres = 6.0;
    }
    memset(propValue, 0, sizeof(propValue));
    if (__system_property_get(PROP_RW_MINIEYE_GPS_SPD_SAT_NO_THRES, propValue) > 0) {
        mSpdSatNumThres = atoi(propValue);
    }

    memset(propValue, 0, sizeof(propValue));
    if (__system_property_get(PROP_PERSIST_MINIEYE_GPS_SPD_TIMEOUT, propValue) > 0) {
        mGpsSpdTimeout = atoi(propValue) * 1000;
    }
}

IOMsgHandler::~IOMsgHandler()
{
    delete mcuAgent;
    delete mQueue;
    delete mLogger;
    delete mGpsSpdCalib;
    nmea_parser_destroy(&mParser);
	stop();
}

bool IOMsgHandler::setDevId(void)
{
    char propValue[PROP_VALUE_MAX];
#define C4_RDWR_PATH "/data/c4/c4.json"
    rapidjson::Document doc;
    RAPIDJSON_LOAD(C4_RDWR_PATH);
    RAPIDJSON_GET_JSON_STRING(doc, "id", mDeviceId);

    logd("get device id %s", mDeviceId.c_str());
    if (__system_property_get(PROP_PERSIST_MINIEYE_DEVICEID, propValue) > 0) {
        if (!strcmp(propValue, mDeviceId.c_str())) {
            memset(propValue, 0, PROP_VALUE_MAX);
            snprintf(propValue, sizeof(propValue), "%s", mDeviceId.c_str());
            return __system_property_set(PROP_PERSIST_MINIEYE_DEVICEID, propValue);
        } else {
            /* 内容不变不修改直接返回成功 */
            return true;
        }
    } else {
        memset(propValue, 0, PROP_VALUE_MAX);
        snprintf(propValue, sizeof(propValue), "%s", mDeviceId.c_str());
        return __system_property_set(PROP_PERSIST_MINIEYE_DEVICEID, propValue);
    }
    
    return true;
}

void IOMsgHandler::get_local_prop(void)
{
    /* gps calib aspeed */
    char value[PROP_VALUE_MAX];
    memset(value, 0, PROP_VALUE_MAX);
    int len;
    if (mGpsCalibEnable) {
        memset(value, 0, sizeof(value));
        len = __system_property_get(PROP_RW_MINIEYE_GPS_CALIB_RATIO, value);
        if(len == 0) {
            mGpsCalibRatio = 0.0;
        } else {
            mGpsCalibRatio = atof(value);
        }

        memset(value, 0, sizeof(value));
        len = __system_property_get(PROP_RW_MINIEYE_CALIB_STDDEV, value);
        if(len == 0) {
            mGpsCalibStddev = 1e6;
        } else {
            mGpsCalibStddev = atof(value);
        }
        mGpsSpdCalib = new GpsSpeedCalib(mGpsCalibRatio, mGpsCalibStddev);
    }
    mLogger->mlog("mGpsCalibEnable %d ratio %f stddev %f",
            mGpsCalibEnable, mGpsCalibRatio, mGpsCalibStddev);

    memset(value, 0, sizeof(value));
    len = __system_property_get(PROP_RW_MINIEYE_GPS_PLAYBACK_ENABLE, value);
    if(len > 0) {
        mGpsPlayBack = atoi(value);
        logd("set gps playback %d", mGpsPlayBack);
    }

    memset(value, 0, sizeof(value));
    len = __system_property_get(PROP_PERSIST_MINIEYE_GPS_NMEM_LOG_ENABLE, value);
    if(len > 0) {
        mGpsNmeaLog = atoi(value);
        logd("set gps nmea log %d", mGpsNmeaLog);
    }

    memset(value, 0, sizeof(value));
    len = __system_property_get(PROP_PERSIST_MINIEYE_GPS_NMEA_TO_DB9, value);
    if(len > 0) {
        mGpsNmeaToDB9 = atoi(value);
        logd("set mGpsNmeaToDB9 %d", mGpsNmeaToDB9);
    }

    memset(value, 0, sizeof(value));
    len = __system_property_get(PROP_PERSIST_MINIEYE_CAMERA_CHANNEL_SHOW, value);
    if (len > 0) {
        mCamCh = strtoul(value, NULL, 16);
        logd("set camera channnel show 0x%08x", mCamCh);
    }
    logd("get camera channnel show 0x%08x", mCamCh);

    memset(value, 0, sizeof(value));
    len = __system_property_get(PROP_PERSIST_MINIEYE_ADAS_DISP_MODE, value);
    if (len > 0) {
        if (!strcmp(value, "normal")) {
            mAdisp_mode = ADISP_NORMAL;
        } else if(!strcmp(value, "speed")) {
            mAdisp_mode = ADISP_SPEED;
        }
    }

    memset(value, 0, sizeof(value));
    len = __system_property_get(PROP_PERSIST_MINIEYE_RS485_EXTERNAL_GPS, value);
    if(len > 0) {
        mRS485ExternalGps = atoi(value);
        logd("set rs485 external gps %d", mRS485ExternalGps);
    }

    memset(value, 0, sizeof(value));
    len = __system_property_get(PROP_PERSIST_MINIEYE_NMEA_DATA_ENABLE, value);
    if (len > 0) {
        mNMEADataEnable = atoi(value);
        logd("set nmea enable %d", mNMEADataEnable);
    }

    memset(value, 0, sizeof(value));
    len = __system_property_get(PROP_PERSIST_MINIEYE_DISABLE_GPS_TIME, value);
    if (len > 0) {
        mEnableGpsTime = atoi(value);
        logd("set mEnableGpsTime %d", mEnableGpsTime);
    }

    memset(value, 0, sizeof(value));
    len = __system_property_get(PROP_PERSIST_SPEED_LIMIT, value);
    if (len > 0) {
        mSpeedLimit = atoi(value);
    }

    logd("set mSpeedLimit %d", mSpeedLimit);
}

/* 有写falsh操作不允许频繁调用 */
void prop_set_backlight(uint32_t level)
{
    char prop[PROP_VALUE_MAX];
    memset(prop, 0, PROP_VALUE_MAX);

    snprintf(prop, sizeof(prop), "%u", level);
    __system_property_set(PROP_PERSIST_MINIEYE_LCD_BACK_LIGHT_TIME, prop);
}
uint32_t prop_get_backlight(void)
{
    uint32_t val = 0;
    char value[PROP_VALUE_MAX];
    memset(value, 0, sizeof(value));
    int len = __system_property_get(PROP_PERSIST_MINIEYE_LCD_BACK_LIGHT_TIME, value);
    if (len > 0) {
        val = atoi(value);
    } else {
        val = 1;
    }
    return val;
}

void IOMsgHandler::set_backlight(uint32_t level)
{
    level = level % BACKLIGHT_TIME_LEVEL_MAX;

    prop_set_backlight(level);
    mGuiConf.backlight_sel = level;
    mLcdBackLightTime = mGuiConf.backLightTime[level];
    logd("set back light %u", mLcdBackLightTime);
}

uint32_t IOMsgHandler::get_backlight(void)
{
    uint32_t level = prop_get_backlight();

    level = level % BACKLIGHT_TIME_LEVEL_MAX;
    mGuiConf.backlight_sel = level;

    return mGuiConf.backLightTime[level];
}

/* 有写flash操作不允许频繁调用 */
void IOMsgHandler::set_cam_channel(uint32_t ch)
{
    char prop[PROP_VALUE_MAX];
    memset(prop, 0, PROP_VALUE_MAX);

    mCamCh = ch;
    logd("set camera channel 0x%08x", mCamCh);
    snprintf(prop, sizeof(prop), "0x%08x", ch);
    __system_property_set(PROP_PERSIST_MINIEYE_CAMERA_CHANNEL_SHOW, prop);
}

uint32_t IOMsgHandler::get_camch_show(void)
{
    return mCamCh;
}
#define APPEND_STAT_MSG(msg, key, fmt, val ...) APPEND_STR_MSG(msg, key, 16, fmt, ## val)
std::string IOMsgHandler::dumpStatStr(void) {
    char idx;
    uint32_t start, end;
    logs.curent_trip_dump(&idx, &start, &end);
    time_t tBgn = start;
    time_t tEnd = end;
    struct tm tmBgn, tmEnd;
    struct tm * pBgn = localtime_r(&tBgn, &tmBgn);
    struct tm * pEnd = localtime_r(&tEnd, &tmEnd);

    std::string msg = "\n";
    APPEND_STAT_MSG(msg, "key", "[0] = %d, [1] = %d", mKey[0], mKey[1]);
    APPEND_STAT_MSG(msg, "gps_time", "%u", mLbs.time);
    APPEND_STAT_MSG(msg, "mLbs", "lng = %f, lat = %f, alt = %.1f, satelite_num = %d, located = %d, dir = %.1f, antenna = %d",
        mLbs.lng_x1kw / 10000000.0, mLbs.lat_x1kw / 10000000.0, mLbs.alt_x10 / 10.0, mLbs.sat, mLbs.status, mLbs.dir_x100 / 100.0, mLbs.antenna);
    APPEND_STAT_MSG(msg, "speed", "%.1f, %.1f", mSpeed_x10 / 10.0, mLbs.speed_x10 / 10.0);
    APPEND_STAT_MSG(msg, "mileage", "%.2f", mTotal_mileage);
    APPEND_STAT_MSG(msg, "vehicle_io", "acc %d alarm %d back %d near %d far %d left %d right %d brake %d door %d can_loss %d",
        vehicle_io.acc, vehicle_io.emergency_alarm, vehicle_io.backwards, vehicle_io.normal_light,
        vehicle_io.far_light, vehicle_io.left_turn, vehicle_io.right_turn, vehicle_io.brake, vehicle_io.door, vehicle_io.can_signal_loss);
    APPEND_STAT_MSG(msg, "dvr_io", "hot %d nopaper %d panel %d inserted %d IdOK %d ID %.18s",
        dvr_io.printer_hot, dvr_io.printer_no_paper, dvr_io.panel_door_open, dvr_io.iccard_inserted, mICCardidOk, iccard_wr.id);
    APPEND_STAT_MSG(msg, "cur_drive_log", "logIndex = %d, [%02d%02d%02d_%02d:%02d:%02d - %02d%02d%02d_%02d:%02d:%02d]",
        idx,
        pBgn->tm_year - 100, pBgn->tm_mon + 1, pBgn->tm_mday, pBgn->tm_hour, pBgn->tm_min, pBgn->tm_sec,
        pEnd->tm_year - 100, pEnd->tm_mon + 1, pEnd->tm_mday, pEnd->tm_hour, pEnd->tm_min, pEnd->tm_sec);
    APPEND_STAT_MSG(msg, "adc", "%2d(%2.2fv)-%2d(%2.2fv), %2d(%2.2fv)-%2d(%2.2fv), %2d(%2.2fv)-%2d(%2.2fv)",
        mAdcRaw[ADC_CHN_24V], mAdcVoltage[ADC_CHN_24V],
        mAdcRaw[ADC_CHN_BAT], mAdcVoltage[ADC_CHN_BAT],
        mAdcRaw[ADC_CHN_EXT_VAL1], mAdcVoltage[ADC_CHN_EXT_VAL1],
        mAdcRaw[ADC_CHN_EXT_VAL2], mAdcVoltage[ADC_CHN_EXT_VAL2],
        mAdcRaw[ADC_CHN_CAP], mAdcVoltage[ADC_CHN_CAP],
        mAdcRaw[ADC_CHN_TEM], mAdcVoltage[ADC_CHN_TEM]);
    APPEND_STAT_MSG(msg, "mcu", "uptime = '%s', temprature = %d", uptime(mMcuUptime).c_str(), mMcuTempture_x100);
    APPEND_STAT_MSG(msg, "power", "sys_pwr = %d (0x%04x), pwr_low = %d, pwr_off = %d",
        mPwrSet.sys_pwr, mPwrSet.val, mSys.power_low, mSys.power_off);
    APPEND_STAT_MSG(msg, "record", "rec status bits = 0x%02x, disk status = 0x%x", mRecStatus, mDiskStatus);
    APPEND_STAT_MSG(msg, "mcu_status", "fmcu_rst_soc:%d, fauto_ctrl_cap:%d, fauto_ctrl_bat:%d",
                    mPwrSet.fmcu_rst_soc, mPwrSet.fauto_ctrl_cap, mPwrSet.fauto_ctrl_bat);
    APPEND_STAT_MSG(msg, "orign", "vehicle_io %04xh, dvr_io %08xh", vehicle_io.value, dvr_io.value);
    return msg;
}
std::string IOMsgHandler::log_level(const char * key, int level)
{
    std::string msg = "\n";
    static const char * logName[LOG_TYPE_MAX] = {"GPS", "STATUS", "CAN", "ASPEED"};
    int i = 0, found = 0;
    if (key) {
        for (i = 0; i < LOG_TYPE_MAX; ++i) {
            if (!strcmp(logName[i], key)) {
                mLogVerbose[i] = level;
                found = 1;
                break;
            }
        }
    }
    if (!found) {
        for (i = 0; i < LOG_TYPE_MAX; ++i) {
            APPEND_STAT_MSG(msg, logName[i], "%d", mLogVerbose[i]);
        }
    }
    return msg;
}
int IOMsgHandler::hal_send(IOMsg& msg, void *context)
{
    IOMsgHandler *io = static_cast<IOMsgHandler *>(context);
    io->send(msg);
    char *p = msg.data;
	//logd("[IOMsgHandler::hal_send] %s\n", my::hex(my::constr(p, msg.data.length())).c_str());
    return 0;
}

void IOMsgHandler::clear_mcu_stack()
{
    uint8_t buf[1200];

    memset(buf, 0, sizeof(buf));
    mQueue->msgEnque(buf, 1200);
}

void IOMsgHandler::request_iccard()
{
    // 读IC卡
    uint8_t buf[MCU_MSG_MAX_SIZE];
    McuMessage *pMsg = (McuMessage *)&buf[0];

    IOMsg msg(MAJOR_IC_CARD, ICCARD_MINOR_READ);
    logi("send ICcard READ Cmd\n");
    send(msg);
}

void IOMsgHandler::query_mcu_version(void)
{
    IOMsg msg(MAJOR_VERSION, VERSION_BASE);
    logd("query mcu version\n");
    send(msg);
}

void IOMsgHandler::bat_thres_config(uint16_t disable_thres, uint16_t enable_thres)
{
    IOMsg msg(MAJOR_BAT_CONFIG, 0);
    bat_threshold_t conf;

    conf.disable_thres = disable_thres;
    conf.enable_thres = enable_thres;
    msg.data.append((const char *)&conf, sizeof(bat_threshold_t));

    logd("config bat threshold\n");
    send(msg);
}


// 开启IO消息处理器
int IOMsgHandler::start()
{
	// 初始化日志
	{
		my::string path(HOME); path.append("/data/logs.dat");
		int r = logs.init(path.c_str());
		if (r < 0)
		{
			logpe("[IOMsgHandler::start] Failed to open '%s', err=[%d].", path.c_str(), r);
			return r;
		}
	}

	// 初始化记录
	{
		my::string path(HOME); path.append("/data/records.dat");
		int r = records.init(path.c_str());
		if (r < 0)
		{
			logpe("[IOMsgHandler::start] Failed to open '%s', err=[%d].", path.c_str(), r);
			return r;
		}
	}
    //logs.fix_trip(1592442027, 1592528427, 1);
    //logs.debug();

    vector<uint8_t> speed;
    uint32_t tick=0;
    records.Get15MinSpeedBeforeStop(speed, tick);

    get_last_gps_location();
    
	return thread::start();
}

// 关闭IO消息处理器
void IOMsgHandler::stop()
{
	thread::stop();

}

// 发送消息
bool IOMsgHandler::send(IOMsg& msg)
{
	if (msg.data.length() < 8)
		return false;

	// 填入长度
	my::ushort len = msg.data.length() - 8;
	msg.data.write(6, len);

	// 计算消息体校验码
	char chksum = 0;
	char* p = &msg.data[8];
	for (my::ushort i = 0; i < len; i++)
	{
		chksum += *p++;
	}
	msg.data.write(5, chksum);

	// 计算消息头校验码
	p = msg.data;
	p[2] = p[3] + p[4] + p[5] + p[6] + p[7];

	//logd("[IOMsgHandler::send] %s\n", my::hex(my::constr(p, msg.data.length())).c_str());

    mQueue->msgEnque(p, msg.data.length());

    return 0;
}

void IOMsgHandler::run()
{
    prctl(PR_SET_NAME, "hostio_loop");
    while (1) {
        bool bRet = mQueue->mainloop();
        if (!bRet) {
            sleep(2);
        }
    }
}

#define PWR_DOWN_PENDING_TIMEOUT_SEC (20)
bool IOMsgHandler::tryPwrOff(struct timespec& now, bool forcePwrDn)
{
    bool PwrCmdReady = true;
    bool pwrDnPendingTimeout = false;
    for(uint32_t i = PWR_DOWN_THREAD_MEDIA; i< PWR_DOWN_THREAD_MAX; i++) {
        if((mPwrDn & (1 << i)) == 0) { /* thread not send pwroff cmd*/
            PwrCmdReady = false;
        }
    }

    if(now.tv_sec > mPwrDnpdingTime.tv_sec + PWR_DOWN_PENDING_TIMEOUT_SEC) {
        pwrDnPendingTimeout = true;
        logi("power down pending timeout!\n");
    }

    if(forcePwrDn || PwrCmdReady || pwrDnPendingTimeout) {
        logw("send pwr down cmd, forcePowerDown %d PwrCmdReady %d pendigTimeout %d\n"\
                , forcePwrDn, PwrCmdReady, pwrDnPendingTimeout);
        mLogger->mlog("+send sys-off, force %d ready %d timeout %d",forcePwrDn, PwrCmdReady, pwrDnPendingTimeout);
        sync();
        mLogger->mlog("-send sys-off, force %d ready %d timeout %d",forcePwrDn, PwrCmdReady, pwrDnPendingTimeout);

        IOMsg msg(MAJOR_GPIO, SYS_PWR_OFF);
        send(msg);
        return true;
    } else {
        return false;
    }
}



void IOMsgHandler::McuMsgFastHandler(void)
{
    uint8_t buf[MCU_MSG_MAX_SIZE];
    McuMessage *pMsg = (McuMessage *)&buf[0];

    logd("Mcu listen pthread enter\n");
    while(1)
    {
        //logd("McuAgent %p pthread read\n", mcuAgent);
        if (mcuAgent->recv(&(pMsg->ipcMsg))) {
            //logd("Mcu listen recv type  %d len %d\n", pMsg->type, pMsg->len);

            switch(pMsg->ipcMsg.type) {
                /* 快处理 */
                case MCU_MSG_TYPE_IC_CARD_RD:
                    {
                        request_iccard();
                    }
                    break;
                case MCU_MSG_TYPE_IC_CARD_WR:
                    {
                        IOMsg msg(MAJOR_IC_CARD, ICCARD_MINOR_WRITE);
                        msg.data.append((const char *)pMsg->u.u8Array, pMsg->ipcMsg.len);
                        logd("send ICcard write Cmd\n");
                        send(msg);
                    }
                    break;
                case MCU_MSG_TYPE_IC_CARD_FIX_RST:
                    {
                        IOMsg msg(MAJOR_IC_CARD, ICCARD_MINOR_FIX_RST_VAL);
                        logd("send ICcard fix rst Cmd\n");
                        send(msg);
                    }
                    break;
                case MCU_MSG_TYPE_PRINTER_TEST:
                    {
                        set_print_info();
                    }
                    break;
                case MCU_MSG_TYPE_PRINT:
                    {
                        tryPrint();
                    }
                    break;
                case MCU_MSG_TYPE_MCU_CONFIG:
                    {
                        if(pMsg->ipcMsg.len > 0) {

                        } else {
                            mMcuInited = false;
                        }
                    }
                    break;
                case MCU_MSG_TYPE_UPGRADE:
                    {
                        logd("send mcu upgrade frame len %d\n", pMsg->ipcMsg.len);
                        IOMsg msg(MAJOR_UPGRADE, 0);
                        msg.data.append((const char *)pMsg->u.u8Array, pMsg->ipcMsg.len);
                        send(msg);
                    }
                    break;
                case MCU_MSG_TYPE_GPS_PASS_ENABLE:
                    {
                        if(pMsg->ipcMsg.len > 0) {
                            uint8_t cmd = pMsg->u.schar[0];
                            if (cmd == 0x0) {
                                mGpsMsgPass = pMsg->u.schar[1];
                                logd("gps pass enable %d\n", mGpsMsgPass);
                            } else if(cmd == 0xff) {
                                UrtpUartConfT *conf = (UrtpUartConfT *)&pMsg->u.schar[1];
                                logd("baud %d stop %d parity %d"\
                                        , conf->baudrate
                                        , conf->stopbit
                                        , conf->parity);
                                IOMsg msg(MAJOR_UART_CONFIG, EXT_UART_GPS);
                                msg.data.append((const char *)conf, sizeof(UrtpUartConfT));
                                send(msg);
                            }
                        }
                    }
                    break;
                case MCU_MSG_TYPE_GPS_CMD:
                    {
                        logd("gps cmd len %d\n", pMsg->ipcMsg.len);
                        IOMsg msg(MAJOR_GPS, 0);
                        msg.data.append((const char *)pMsg->u.u8Array, pMsg->ipcMsg.len);
                        send(msg);
                    }
                    break;
                case MCU_MSG_TYPE_CONFIG_CAN:
                    {
                        McuMsgCanConfigT *p = (McuMsgCanConfigT *)pMsg->u.uchar;
                        if (pMsg->ipcMsg.len < sizeof(McuMsgCanConfigT)) {
                           break;
                        }

                        if (p->use_json) {
                            logd("using can json  %s\n", p->file);
                            mCan.reloadConfig(p->file);
                        } else {
                            logd("set baud %d, mode:%d!\n", p->speed, p->mode);
                            mCan.setCanMode(p->canx, p->mode, p->speed);
                        }
                    }
                    break;
                case MCU_MSG_TYPE_SET_CAN_FILTER:
                    {
                        static std::map<uint8_t, uint8_t> mp {
                            {MCU_CAN_IDX_CAN0_SPEED_RES1, MCU_CAN_IDX_CAN0_FILTER_RES1},
                            {MCU_CAN_IDX_CAN1_DISP_RES1, MCU_CAN_IDX_CAN1_FILTER_RES1},
                            {MCU_CAN_IDX_CAN0_SPEED_RES2, MCU_CAN_IDX_CAN0_FILTER_RES2},
                            {MCU_CAN_IDX_CAN1_DISP_RES2, MCU_CAN_IDX_CAN1_FILTER_RES2},
                            {MCU_CAN_IDX_CAN0_SPEED_RES3, MCU_CAN_IDX_CAN0_FILTER_RES3},
                            {MCU_CAN_IDX_CAN1_DISP_RES3, MCU_CAN_IDX_CAN1_FILTER_RES3},
                        };
                        McuMsgCanFilterT* canFilter = (McuMsgCanFilterT*)pMsg->u.filter;
                        uint8_t canChannel = canFilter->canIdx;
                        uint32_t id[4] = {0};
                        id[0] = canFilter->canIds[0];
                        bool bListMode = canFilter->useListMode ? true : false;
                        if (!bListMode) {
                            id[1] = canFilter->canIds[1];
                        }
                        logd("can++ filter canID %d, frameID %x, bListMode:%d mask:%x", canChannel, id[0], bListMode, id[1]);

                        mCan.setCanFilter((McuMsgCanIdxE)canChannel, true, bListMode, (FilterNum_E)mp[canChannel], id, !bListMode);

                    }
                    break;
                case MCU_MSG_TYPE_CLEAR_ALL_FILTER:
                    {
                        mCan.clearAllCanFilterAndStart();
                    }
                    break;
                case MCU_MSG_TYPE_RAW_CAN0:
                    {
                        /* speed can */
                        IOMsg msg(MAJOR_CAN, CAN_MINOR_SPEED_CAN0_MSG);
                        msg.data.append((const char *)pMsg->u.u8Array, pMsg->ipcMsg.len);
                        send(msg);
                    }
                    break;
                case MCU_MSG_TYPE_RAW_CAN1:
                    {
                        IOMsg msg(MAJOR_CAN, CAN_MINOR_DISP_CAN1_MSG);
                        msg.data.append((const char *)pMsg->u.u8Array, pMsg->ipcMsg.len);
                        send(msg);
                        //logd("send disp.\n");
                    }
                    break;
                case MCU_MSG_TYPE_IO_CTRL:
                    {
                        uint8_t minor;
                        if(pMsg->ipcMsg.len > 0) {
                            minor = pMsg->u.u8Array[0];
                            IOMsg msg(MAJOR_GPIO, minor);
                            send(msg);
                        }
                    }
                    break;
                case MCU_MSG_TYPE_PWR_DOWN:
                    {
                        if(pMsg->ipcMsg.len < 0) {
                            break;
                        }
                        if(pMsg->u.u8Array[0] >= PWR_DOWN_THREAD_MAX) {
                            logd("msg(%d) invalid\n", MCU_MSG_TYPE_PWR_DOWN);
                        }
                        logi("pthread %d send pwr down.", pMsg->u.u8Array[0]);
                        MY_SPINLOCK_X(mPwrlock);
                        mPwrDn |= 1 << pMsg->u.u8Array[0];
                    }
                    break;
                case MCU_MSG_TYPE_PWR_DOWN_PENDING:
                    {
                        if(pMsg->ipcMsg.len < 0) {
                            break;
                        }
                        if(pMsg->u.u8Array[0] >= PWR_DOWN_THREAD_MAX) {
                            logd("msg(%d) invalid\n", MCU_MSG_TYPE_PWR_DOWN);
                        }
                        MY_SPINLOCK_X(mPwrlock);
                        mPwrDnPending |= 1 << pMsg->u.u8Array[0];

                        logd("pending...\n");
                        /* update pending time*/
                        clock_gettime(CLOCK_MONOTONIC, &mPwrDnpdingTime);
                    }
                    break;
                case MCU_MSG_TYPE_RS485:
                    {
                        IOMsg msg(MAJOR_UART, EXT_UART_RS485);
                        msg.data.append((const char *)pMsg->u.u8Array, pMsg->ipcMsg.len);
                        send(msg);
                        logd("send rs485\n");
                    }
                    break;
                case MCU_MSG_TYPE_RS485_CONFIG:
                    {
                        UrtpUartConfT *conf = (UrtpUartConfT *)&pMsg->u.schar[0];
                        logd("baud %d stop %d parity %d"\
                                , conf->baudrate
                                , conf->stopbit
                                , conf->parity);
                        IOMsg msg(MAJOR_UART_CONFIG, EXT_UART_RS485);
                        msg.data.append((const char *)conf, sizeof(UrtpUartConfT));
                        send(msg);
                        logd("send rs485\n");
                    }
                    break;
                case MCU_MSG_TYPE_DB9:
                    {
                        IOMsg msg(MAJOR_UART, EXT_UART_DB9);
                        msg.data.append((const char *)pMsg->u.u8Array, pMsg->ipcMsg.len);
                        send(msg);
                        logd("send db9\n");
                    }
                    break;
                case MCU_MSG_TYPE_GB19056_LOG_DUMP:
                    {
                        logs.debug();
                    }
                    break;
                case MCU_MSG_TYPE_MCU_VERSION:
                    {
                        query_mcu_version();
                    }
                    break;
                case MCU_MSG_TYPE_MCU_FIRMWARE_INFO:
                    {
                        uint8_t buf[MCU_MSG_MAX_SIZE];
                        McuMessage *pMsg = (McuMessage *)&buf[0];
                        IOMsg msg(MAJOR_VERSION, VERSION_FIRMWARE_INFO);
                        logd("query mcu firmware info\n");
                        send(msg);
                    }
                    break;
                case MCU_MSG_TYPE_HOSTIO_VERSION:
                    {

                    }
                    break;
                case MCU_MSG_TYPE_LOG_CTRL:
                    {
                        if(pMsg->ipcMsg.len < 2) {
                            break;
                        }
                        if (pMsg->u.uchar[0] <0 || pMsg->u.uchar[0] >= LOG_TYPE_MAX) {
                            logd("MCU_MSG_TYPE_LOG_CTRL invalid");
                            break;
                        }
                        mLogVerbose[pMsg->u.uchar[0]] = pMsg->u.uchar[1];

                        if (pMsg->u.uchar[0] == LOG_TYPE_CAN) {
                            mCan.mVerbose = pMsg->u.uchar[1];
                        }
                    }
                    break;
                case MCU_MSG_TYPE_MCU_RST:
                    {
                        logd("send reset mcu cmd\n");
                        IOMsg msg(MAJOR_RESET, 0);
                        send(msg);
                    }
                    break;
                case MCU_MSG_TYPE_MCU_UPGRD_AUTHEN:
                    {
                        logd("send mcu upgrade authen cmd\n");
                        IOMsg msg(MAJOR_VERSION, VERSION_AUTHEN);
                        msg.data.append((const char *)pMsg->u.uchar, pMsg->ipcMsg.len);
                        send(msg);
                    }
                    break;
                case MCU_MSG_TYPE_MCU_GB19056_FETCH_ALL:
                    {
                        logd("fetch gb19056 log\n");
					    gbt19056.usbFetchData();
                    }
                    break;
                case MCU_MSG_TYPE_LCD_CTRL:
                    {
                        if(pMsg->ipcMsg.len < 1) {
                            break;
                        }
                        logd("lcd ctrl %d\n", pMsg->u.uchar[0]);
                        IOMsg msg(MAJOR_LCD, pMsg->u.uchar[0]);
                        send(msg);
                    }
                    break;
                case MCU_MSG_TYPE_ADAS_CAN700:
                    {
                        //logd("adas can700\n");
                        ADAS_CAN700 *p = (ADAS_CAN700 *)&pMsg->u.uchar[0];

                        if (mAdisp_mode == ADISP_NORMAL) { /*adas display speed mode */
                            mCan.adas_alert_disp(p, mSpeed_x10);
                        }
                    }
                    break;
                case MCU_MSG_TYPE_CAN1_DISPLAY: {
                    if (!access("/mnt/obb/dump_bsd_can_data", R_OK)) {
                        my::constr data(pMsg->u.schar, sizeof(UrtpCanT));
                        logd("MCU_MSG_TYPE_CAN1_DISPLAY %s", my::hex(data, true).c_str());
                    }
                    mCan.sendMsg(MCU_CAN_IDX_CAN1_DISP_RES1, (UrtpCanT*)(pMsg->u.uchar));
                    break;
                }
                case MCU_MSG_TYPE_MCU_GB19056_FETCH: {
                    my::datetime tick;
                    my::uchar cmd;
                    my::uint bgn = 0, end = 0;
                    my::ushort n = 0;
                    const char * p = (const char *)&pMsg->u.uchar[0];

                    cmd = p[0];
                    p += 7;
                    if (pMsg->ipcMsg.len >= 14) {
                        // 开始时间
                        bcd2datetime(tick, p);
                        bgn = tick.to_utc_seconds() - TIME_ZONE_OFFSET;
                        // 结束时间
                        bcd2datetime(tick, p);
                        end = tick.to_utc_seconds() - TIME_ZONE_OFFSET;
                        // 数量
                        my::constr str(p, 2);
                        str >> my::hton >> n;
                    }
                    MsgBase msg;
                    char fileName[128];
                    snprintf(fileName, sizeof(fileName), "/mnt/obb/.%02xH.dat", cmd);
                    unlink(fileName);
                    logd("cmd 0x%02x, bgn %d, end %d, n %d, %s", cmd, bgn, end, n, fileName);
                    if (gbt19056.packMsg(msg, cmd, bgn, end, n)) {
                        FILE * fp = fopen(fileName, "w+");
                        if (fp) {
                            int size = fwrite(msg.data.c_str(), 1, msg.data.length(), fp);
                            if (size != msg.data.length()) {
                                loge("fwrite %s fail! %d != %d\n", size, msg.data.length());
                            }
                            fclose(fp);
                        }
                        else {
                            loge("open %s fail!", fileName);
                        }
                    }
                    else {
                        loge("gbt19056.packMsg fail!");
                    }
                    break;
                }
                case MCU_MSG_TYPE_EXT_LBS: {
                    mLbs = pMsg->u.lbs[0];
                    if (mLbs.status > 0) {
                        update_last_gps_location();
                        clock_gettime(CLOCK_MONOTONIC, &mGpsUpdateTime);
                    }
                    calib_aspeed();
                    break;
                case MCU_MSG_TYPE_CAN_STAT:
                    {
                        IOMsg msg(MAJOR_CAN, CAN_MINOR_STATISTICS);
                        logd("query mcu can statistics\n");
                        send(msg);
                    }
                    break;
                case MCU_MSG_TYPE_CAN_CLR_STAT:
                    {
                        IOMsg msg(MAJOR_CAN, CAN_MINOR_CLEAR_STATISTICS);
                        logd("clear mcu can statistics\n");
                        send(msg);
                    }
                    break;
                case MCU_MSG_TYPE_MCU_REG:
                    {
                        logd("get mcu reg(0x%X) value.(len:%d)\n", *(uint32_t *)(pMsg->u.u8Array), pMsg->ipcMsg.len);
                        IOMsg msg(MAJOR_GET_REG, 0);
                        msg.data.append((const char *)pMsg->u.u8Array, pMsg->ipcMsg.len);
                        send(msg);
                    }
                    break;
                }
                default:
                    logd("Mcu listen recv unknow...%d\n", pMsg->ipcMsg.type);
                    break;
            }
        } else {
            logd("Mcu listen recv fail\n");
        }
    }

}

// 处理收到的未转义原始消息包
bool IOMsgHandler::on_recv(const char* data, int size)
{
	const char* p = data;
	const char* q = data + size;
	if (size <= 0) return false;

	while (p < q)
	{
		switch (stack.state)
		{
		case 0: // 查找0x13
			{
				while (p < q && *p != (char)0x13) p++;
				if (p >= q) break;
				stack.state = 1;
				p++;
			}
			break;
		case 1: // 查找0x14
			{
				if (*p++ != (char)0x14)
					stack.state = 0;
				else
				{
					stack.state = 2;
					stack.header.len = 0;
				}
			}
			break;
		case 2: // 累积消息头
			{
				while (p < q && stack.header.len < 6) stack.header.data[stack.header.len++] = *p++;
				if (stack.header.len == 6) // 检查消息头校验码
				{
					char chksum = stack.header.data[1] + stack.header.data[2] + stack.header.data[3] + stack.header.data[4] + stack.header.data[5];
					if (chksum != stack.header.data[0])
					{
						logw("[IOMsgHandler::on_recv] Bad message header.");
						char buf[6];
						memcpy(buf, stack.header.data, 6);

						stack.state = 0;
						on_recv(buf, 6);
						break;
					}
					else
					{
						// 获取长度
						my::ushort n;
						my::constr d(stack.header.data+4, 2);
						d >> my::ntoh >> n;

						if (n != 0) // 如果长度不为0
						{
							stack.body = "";
							stack.state = 3;
						}
						else
						{
							proc(stack.header.data[1], stack.header.data[2], 0, 0);
							stack.state = 0;
						}
					}
				}
			}
			break;
		case 3: // 累积消息体
			{
				// 获取长度
				my::ushort n;
				my::constr d(stack.header.data + 4, 2);
				d >> my::ntoh >> n;

				my::ushort c = (my::ushort)(q - p);

				const char* body = stack.body;
				my::ushort bodylen = stack.body.length();
				if (bodylen + c >= n) // 长度够了
				{
					// 计算消息体检验码
					char chksum = 0;
					for (my::ushort i = 0; i < bodylen; i++)
					{
						chksum += body[i];
					}

					c = n - bodylen;
					for (my::ushort i = 0; i < c; i++)
					{
						chksum += p[i];
					}

					// 如果消息体校验码正确
					if (stack.header.data[3] == chksum)
					{
					    stack.body.append(p, c);
				        bodylen = stack.body.length();

	                    //logd("[IOMsgHandler::head] %s\n", my::hex(my::constr(stack.header.data, 6)).c_str());
	                    //logd("[IOMsgHandler::body] %s\n", my::hex(my::constr(body, bodylen)).c_str());

                        /*body append 之后body指针没有更新， 直接使用body.c_str()*/
	                    //logd("[IOMsgHandler::body] %s\n", my::hex(my::constr(stack.body.c_str(), bodylen)).c_str());
						proc(stack.header.data[1], stack.header.data[2], stack.body.c_str(), bodylen);
						p += c;
						stack.state = 0;
					}
					else // 不正确的情况下需要重新检查
					{
						logw("[IOMsgHandler::on_recv] Bad message body.");

						// 压入消息体头
						char buf[6];
						memcpy(buf, stack.header.data, 6);

						stack.state = 0;
						on_recv(buf, 6);

						// 压入消息体
						if (bodylen > 0)
						{
							on_recv(body, bodylen);
						}
					}
				}
				else // 长度不够
				{
					stack.body.append(p, c);
					p += c;
				}
			}
			break;
		}
	}
	return true;
}

void IOMsgHandler::test(my::uint now, my::uint now_ms, int lat_x1kw, int lng_x1kw, int alt_x10, int dir_x100, int speed_x10, float mileage, my::ushort io, char ic_insert, char id_valid, char id[18])
{
	static char cert[18] = {0};
	static float last_mileage = 0;

	IOStatus iostat;
	iostat.value = io;

	LBS loc;
	loc.status = (lng_x1kw != 0 && lng_x1kw != 0);
	loc.lat_x1kw = lat_x1kw;
	loc.lng_x1kw = lng_x1kw;
	loc.alt_x10 = alt_x10;
	loc.dir_x100 = dir_x100;

	logs.update_speed_status_log(now, mLbs.speed_x10, mSpeed_x10);
	LoginIndex idx = logs.update_login_log(now, ic_insert, mICCardidOk, id, cert, -1);
	float total_mileage = records.update(now, now_ms, loc, idx, mSpeed_x10, iostat);
	logs.update(now, now_ms, loc.status, lat_x1kw, lng_x1kw, alt_x10, id, mSpeed_x10, total_mileage, iostat);
	last_mileage = mileage;
}

void get_net_status(int csq[2], int net_info[SIM_MAX])
{
    char prop[PROP_VALUE_MAX] = "";
    memset(prop, 0, PROP_VALUE_MAX);

    if (__system_property_get(PROP_RW_MINIEYE_SIGNAL_LEVEL, prop)) {
        sscanf(prop, "%d", &csq[0]);
    } else {
        csq[0] = 0;
    }
}

void get_iccid(char *iccid)
{
    char prop[PROP_VALUE_MAX] = "";
    memset(prop, 0, PROP_VALUE_MAX);
    __system_property_get(PROP_PERSIST_MINIEYE_ICCID, prop);
    strcpy(iccid,prop);
}

void get_imei(char *imei)
{
    char prop[PROP_VALUE_MAX] = "";
    memset(prop, 0, PROP_VALUE_MAX);
    __system_property_get(PROP_PERSIST_MINIEYE_IME, prop);
    strcpy(imei,prop);
}

void get_debug_speed(int &speed)
{
    int fake_speed=0;
    char prop[PROP_VALUE_MAX] = "";
    memset(prop, 0, PROP_VALUE_MAX);
    if(__system_property_get(PROP_RW_MINIEYE_FAKESPEED, prop)){
        sscanf(prop, "%d", &fake_speed);
        if(fake_speed >= 0)
            speed = fake_speed;
    }
}

void IOMsgHandler::update_last_gps_location(void)
{
    double lat_x1kw, lng_x1kw;
    char prop[PROP_VALUE_MAX] = "";
    memset(prop, 0, PROP_VALUE_MAX);

    lat_x1kw = mLbs.lat_x1kw/10000000.0;
    lng_x1kw = mLbs.lng_x1kw/10000000.0;

    snprintf(prop, sizeof(prop), "Location %f,%f", lat_x1kw, lng_x1kw);
    __system_property_set(PROP_RW_MINIEYE_GPSLASTLOCATION, prop);
}

/* 不要修改i此内容，
 * 外部脚本监测property，用于进入aging模式*/
void IOMsgHandler::set_power_vol(void)
{
    double lat_x1kw, lng_x1kw;
    char prop[PROP_VALUE_MAX] = "";
    memset(prop, 0, PROP_VALUE_MAX);

    snprintf(prop, sizeof(prop), "power/acc/ext1/ext2 %.1fv,%d,%.1fv,%.1fv", mAdcVoltage[ADC_CHN_24V], vehicle_io.acc, mAdcVoltage[ADC_CHN_EXT_VAL1], mAdcVoltage[ADC_CHN_EXT_VAL2]);
    __system_property_set(PROP_RW_MINIEYE_POWER_VOL, prop);
}

bool IOMsgHandler::queryLastGps(int &lat_x1kw, int &lng_x1kw, int &alt_x10, int count)
{
    int ret = records.query_location_records(lat_x1kw, lng_x1kw, alt_x10, count);
    
    return !!ret;
}

/* 重启后使用19056记录初始化默认GPS坐标必须在初始化19056记录后 */
void IOMsgHandler::get_last_gps_location(void)
{
    double lat_x1kw, lng_x1kw;
    double lat_x1kw_df = 22.536779;
    double lng_x1kw_df = 113.95273;
    char prop[PROP_VALUE_MAX] = {0};

    int lat = 0; 
    int lng = 0;
    int alt = 0;
    
    if (__system_property_get(PROP_RW_MINIEYE_GPSLASTLOCATION, prop)) {
        sscanf(prop, "Location %lf,%lf", &lat_x1kw, &lng_x1kw);
        mLbs.lat_x1kw = (int)(lat_x1kw * 10000000);
        mLbs.lng_x1kw = (int )(lng_x1kw * 10000000);
        logd("get last gps location %lf %lf, int %d %d\n", lat_x1kw, lng_x1kw, mLbs.lat_x1kw, mLbs.lng_x1kw);
    } else if (queryLastGps(lat, lng, alt)) {
        /* 从19056获取上一次定位的gps */  
        if (lat == 0 && lng == 0) {
            /* 设备无19056记录 */
            snprintf(prop, sizeof(prop), "Location %f,%f", lat_x1kw_df, lng_x1kw_df);
            __system_property_set(PROP_RW_MINIEYE_GPSLASTLOCATION, prop);
            mLbs.lat_x1kw = lat_x1kw_df*10000000;
            mLbs.lng_x1kw = lng_x1kw_df*10000000;
            mLogger->mlog("no 19056 data use default gps location\n");
        } else {
            snprintf(prop, sizeof(prop), "Location %f,%f", lat / 10000000.00, lng / 10000000.00);
            __system_property_set(PROP_RW_MINIEYE_GPSLASTLOCATION, prop);
            mLbs.lat_x1kw = lat;
            mLbs.lng_x1kw = lng;
            mLogger->mlog("use 19056 data init property, %s!\n", prop);
        }
    } else if (__system_property_get(PROP_PERSIST_MINIEYE_GPSLASTLOCATION, prop)) {
        sscanf(prop, "Location %lf,%lf", &lat_x1kw, &lng_x1kw);
        mLbs.lat_x1kw = (int)(lat_x1kw * 10000000);
        mLbs.lng_x1kw = (int )(lng_x1kw * 10000000);
        logd("get last gps location %lf %lf, int %d %d\n", lat_x1kw, lng_x1kw, mLbs.lat_x1kw, mLbs.lng_x1kw);
    } else { /* never location, use default gps location */
        snprintf(prop, sizeof(prop), "Location %f,%f", lat_x1kw_df, lng_x1kw_df);
        __system_property_set(PROP_RW_MINIEYE_GPSLASTLOCATION, prop);
        mLbs.lat_x1kw = lat_x1kw_df*10000000;
        mLbs.lng_x1kw = lng_x1kw_df*10000000;
        mLogger->mlog("use default gps location\n");
    }
}

string utcToCstTimestamp(time_t s)
{
    char buffer[64];
    time_t t = s;
    struct tm* timeinfo;

    memset(buffer, 0, sizeof buffer);
    timeinfo = localtime(&t);
    strftime(buffer, sizeof buffer, "%Y-%m-%d %H:%M:%S", timeinfo);
    return buffer;
}

string getTimestamp(time_t t)
{
    char buffer[64];
    struct tm* timeinfo;

    memset(buffer, 0, sizeof buffer);
    timeinfo = localtime(&t);
    strftime(buffer, sizeof buffer, "%Y-%m-%d %H:%M:%S", timeinfo);
    return buffer;
}

void IOMsgHandler::set_print_info()
{
#define SPEED_NORMAL        "\xd5\xfd\xb3\xa3"
#define SPEED_ABNORMAL      "\xd2\xec\xb3\xa3"

    uint32_t line = 0;
    mPrint.oneline_str[line] = "\xb3\xb5\xc5\xc6\xba\xc5: "; /*车牌号*/
    conf_t& sys = Manager::getInstance().sys;
    if(sys.vehicle.plate_num.length() != 0) {
        mPrint.oneline_str[line++] += sys.vehicle.plate_num;
    }

    mPrint.oneline_str[line] = "\xb3\xb5\xc5\xc6\xc0\xe0\xd0\xcd: "; /*车牌分类*/
    if(sys.vehicle.plate_type.length() != 0) {
        mPrint.oneline_str[line++] += sys.vehicle.plate_type;
    }

    mPrint.oneline_str[line] = "\xB3\xB5\xC1\xBE\x56\x49\x4E\xBA\xC5: "; /*车牌VIN号*/
    mPrint.oneline_str[line++] += sys.vehicle.vin;

    mPrint.oneline_str[line] = "\xBC\xDD\xCA\xBB\xD4\xB1\xD0\xD5\xC3\xFB: "; /*驾驶员姓名*/

    char name[128] = {0};
#if 1
    my::utf8ToGbk(iccard_wr.name, name);
#else
    my::conf::ini::CCConvert("gbk", name, sizeof(name), "UTF-8", iccard_wr.name, sizeof(iccard_wr.name));
#endif
    mPrint.oneline_str[line++].append(name);

    mPrint.oneline_str[line] = "\xbc\xdd\xca\xbb\xd6\xa4: "; /*驾驶证*/
    mPrint.oneline_str[line++].append(iccard_wr.id, sizeof(iccard_wr.id));

    mPrint.oneline_str[line] = "\xcb\xd9\xb6\xc8\xd7\xb4\xcc\xac: "; /*速度状态*/
    mPrint.oneline_str[line++] += SPEED_NORMAL;

    mPrint.oneline_str[line] = "\xb4\xf2\xd3\xa1\xca\xb1\xbc\xe4: "; /*打印时间*/
    mPrint.oneline_str[line++] += getTimestamp(time(NULL));


    //停车时间

    vector<uint8_t> speedBeforeStop;
    uint32_t tick=0;
    bool ok = records.Get15MinSpeedBeforeStop(speedBeforeStop, tick);
    logd("Get15MinSpeedBeforeStop %d size %d\n", ok, speedBeforeStop.size());
    if(ok) {
        mPrint.oneline_str[line] = "\xCD\xA3\xB3\xB5\xca\xb1\xbc\xe4: "; /*停车时间*/
        mPrint.oneline_str[line++] += getTimestamp(tick);
        /*自停车前15分钟内每分钟平均速度*/
        mPrint.oneline_str[line++] = "\xD7\xD4\xCD\xA3\xB3\xB5\xC7\xB0\x31\x35\xB7\xD6\xD6\xD3\xC4\xDA\xC3\xBF\xB7\xD6\xD6\xD3\xC6\xBD\xBE\xF9\xCB\xD9\xB6\xC8";

        tick = tick/60 * 60;
        char speedStr[64];
        for(vector<uint8_t>::iterator it = speedBeforeStop.begin(); it!=speedBeforeStop.end(); ++it) {
            mPrint.oneline_str[line] = "    ";
            mPrint.oneline_str[line] += getTimestamp(tick);
            snprintf(speedStr, sizeof(speedStr), " %4dkm/h", *it);
            mPrint.oneline_str[line++] += speedStr;
            tick -= 60;
        }
    }


    mPrint.oneline_str[line++] = "\x32\xB8\xF6\xC8\xD5\xC0\xFA\xCC\xEC\xC4\xDA\xB3\xAC\xCA\xB1\xBC\xDD\xCA\xBB\xBC\xC7\xC2\xBC: "; /*2个日历天内超时驾驶记录*/
    // 查询结果
    std::vector<LongTrip> res;
    my::uint end = Manager::getInstance().utc_seconds();
    my::uint start = end < 86400 * 2 ? 0 : end - 86400 * 2;
    logd("-------query start %d end %d --------\n", start, end);
    logs.query_long_trip_logs(res, start, end, 2);

    logd("----------res size %d---------\n", res.size());

    if(res.size() == 0){
        mPrint.oneline_str[line] = "\xCE\xDE\xB3\xAC\xCA\xB1\xBC\xDD\xCA\xBB\xBC\xC7\xC2\xBC"; /*无超时驾驶记录*/
    } else if(res.size() >= 1){
        mPrint.oneline_str[line] = "\xbc\xdd\xca\xbb\xd6\xa4: "; /*驾驶证*/
        mPrint.oneline_str[line++].append(iccard_wr.id, sizeof(iccard_wr.id));
        mPrint.oneline_str[line] = "\xbf\xaa\xca\xbc\xca\xb1\xbc\xe4: "; /*开始时间*/
        my::datetime tick;
        const uint8_t *p = res[0].start_bcd_time;
        bcd2datetime(tick, p);
        mPrint.oneline_str[line++] += utcToCstTimestamp(tick.to_utc_seconds() - TIME_ZONE_OFFSET);

        mPrint.oneline_str[line] = "\xbd\xe1\xca\xf8\xca\xb1\xbc\xe4: "; /*结束时间*/
        p = res[0].end_bcd_time;
        bcd2datetime(tick, p);
        mPrint.oneline_str[line++] += utcToCstTimestamp(tick.to_utc_seconds() - TIME_ZONE_OFFSET);
    } else if(res.size() >= 2) {
        mPrint.oneline_str[line] = "\xbc\xdd\xca\xbb\xd6\xa4: "; /*驾驶证*/
        mPrint.oneline_str[line++].append(iccard_wr.id, sizeof(iccard_wr.id));
        mPrint.oneline_str[line] = "\xbf\xaa\xca\xbc\xca\xb1\xbc\xe4: "; /*开始时间*/
        my::datetime tick;
        const uint8_t *p = res[1].start_bcd_time;
        bcd2datetime(tick, p);
        mPrint.oneline_str[line++] += utcToCstTimestamp(tick.to_utc_seconds() - TIME_ZONE_OFFSET);

        mPrint.oneline_str[line] = "\xbd\xe1\xca\xf8\xca\xb1\xbc\xe4: "; /*结束时间*/
        p = res[1].end_bcd_time;
        bcd2datetime(tick, p);
        mPrint.oneline_str[line++] += utcToCstTimestamp(tick.to_utc_seconds()- TIME_ZONE_OFFSET);
    }
    mPrint.oneline_str[line++] = "   "; /*空行*/

    mPrint.oneline_str[line++] = "\xC7\xA9\xC3\xFB:  _ _ _ _ _ _ _ _"; /*签名:  ________*/

    mPrint.mTotal_line = line;
    logd("----------line %d-----------\n", line);
    for(uint32_t i=0; i<line; i++)
        logd("printer: %s\n", mPrint.oneline_str[i].c_str());

    logd("-----------------------------\n");
}

void IOMsgHandler::send_trip_alert(void)
{
    uint8_t buf[MCU_MSG_MAX_SIZE];
    McuMessage *pMsg = (McuMessage *)&buf[0];
    pMsg->ipcMsg.len = 0;
    pMsg->ipcMsg.type = MCU_MSG_TYPE_TRIP_ALERT;
    mcuAgent->send(&(pMsg->ipcMsg));
}

void IOMsgHandler::uart_config(int type, int baud)
{
    UrtpUartConfT conf(baud, 1, 'N');

    IOMsg msg(MAJOR_UART_CONFIG, type);
    msg.data.append((const char *)&conf, sizeof(UrtpUartConfT));
    send(msg);
}
void IOMsgHandler::mcu_config(void)
{
    if(!mMcuVersionOk)
        query_mcu_version();
    else {
        if(!mMcuInited) {
            mMcuInited = true;

            /*config mcu*/
            logd("Can configed.\n");
            mCan.McuCanConfig();
            bat_thres_config(7000, 6500);
            //uart_config(EXT_UART_DB9);
        }
    }
}

void IOMsgHandler::gui_flush(char key)
{
    // 刷新菜单
    func_last = func;
    func = gs.top();

    if (func) func(this, key, func != func_last);
}

uint8_t IOMsgHandler::get_keyTime()
{
    return mKey[1];
}
void IOMsgHandler::cur_trip_info_sync(uint32_t start, uint32_t end, uint32_t todayTotal)
{
    if ((mTripSyncSec.elapsed() > (10 * 1000)) ||
        !end || !start || (end < start) ||
        ((end - start) > 2 * 24 * 3600)) {
        logd("wait %f ms ...", mTripSyncSec.elapsed());
        return;
    }
    TripInfoT ti;
    ti.startTime = start;
    ti.endTime   = end;
    ti.todayTotal = todayTotal;
    broadcast_msg(MCU_MSG_TYPE_TRIP_INFO, (const char*)&ti, sizeof(TripInfoT));
}
void IOMsgHandler::record_status_chk(void)
{
    struct timespec now;
    clock_gettime(CLOCK_MONOTONIC, &now);
    if (now.tv_sec > mRecStatusChkTime.tv_sec + 10) {
        //todo
        bool stat1 = false;
        char cmd1[64] = {0};
        char resp1[64] = {0};
        // 返回的字符解析
        int ret_str1[3];
        Manager &m = Manager::getInstance();
        for (int i = 0; i < m.sys.cameras + m.sys.ipcs; i++) {
            sprintf(cmd1, "cmd recStatus %d %d", i, m.sys.ch[i].record.video_input);
            stat1 = LogCallProxyCmd::sendReq("recorder", cmd1, resp1, sizeof(resp1), 1);
            if (stat1) { // 远程调用成功
                sscanf(resp1, "%d %d %d", &ret_str1[0], &ret_str1[1], &ret_str1[2]);
                // 读取录像状态
                if (ret_str1[2] == 1) {
                    mRecStatus |= 1 << i;
                }
                else if (ret_str1[2] == 0){
                    mRecStatus &= ~(1 << i);
                }
            } else {
                    // 远程调用失败，直接退出
                    mRecStatus = 0;
                    break;
            }
        }
        mDiskStatus = 0;
        FILE * fp = popen("mount|grep media_rw|cut -d ' ' -f 3", "r");
        if (fp) {
            char line[256] = {0};
            while (fgets(line, sizeof(line), fp)) {
                if (strstr(line, "/disk1")) {
                    mDiskStatus |= 0b01;
                } else if (strstr(line, "/sdcard1")) {
                    mDiskStatus |= 0b0100;
                }
                memset(line, 0, sizeof(line));
            }
            pclose(fp);
        }
        if (!(mDiskStatus & 0b01) && !access("/mnt/obb/ldisk", R_OK)) {
            FILE * pFile = fopen("/sys/block/sda/device/vendor", "r");
            if (pFile) {
                char vendor[128] = {0};
                fread(vendor, sizeof(vendor), 1, pFile);
                if (!strstr(vendor, "USB TO")) {
                    mDiskStatus |= 0b10;
                }
                fclose(pFile);
            }
        }
        if (!(mDiskStatus & 0b0100) && !access("/mnt/obb/lsdcard", R_OK)) {
            mDiskStatus |= 0b1000;
        }
        uint32_t status = ((mDiskStatus << 16) | mRecStatus);
        broadcast_msg(MCU_MSG_TYPE_RECORD_STATUS, (const char*)&status, sizeof(status));
        clock_gettime(CLOCK_MONOTONIC, &mRecStatusChkTime);
    }
}

void IOMsgHandler::mcu_car_info_sync(void)
{
    McuMsgCarInfoT carInfo;
    carInfo.canTurn = mCan.mCarInfo.turn;
    carInfo.canSpeed = mCan.mCarInfo.speed;
    carInfo.canBrake = mCan.mCarInfo.brake;
    carInfo.canMileSingle = mCan.mCarInfo.mileSingle;
    carInfo.canMileTotal = mCan.mCarInfo.mileTotal;
    carInfo.canFuelLeft = mCan.mCarInfo.fuelLeft * mTankSize;
    carInfo.canFuelAvg = mCan.mCarInfo.fuelAvg;
    carInfo.canFuelTotal = mCan.mCarInfo.fuelTotal;
    carInfo.canTurnSpeed = mCan.mCarInfo.turnSpeed;
    carInfo.canDoorBits = mCan.mCarInfo.doorBits;
    carInfo.canReverse = mCan.mCarInfo.reverse;
    broadcast_msg(MCU_MSG_TYPE_CAR_INFO, (const char*)&carInfo, sizeof(carInfo));
}

void IOMsgHandler::mcu_status_sync(void)
{
    struct timespec now;
    clock_gettime(CLOCK_MONOTONIC, &now);

    /* gps 超时清零 */
    if (now.tv_sec > mGpsUpdateTime.tv_sec + 10) {
        mLbs.status = 0;
        mLbs.sig_level = 0;
        mLbs.sat = 0;
        //logd("gps recv info timeout 10sec ! %d\n", mGpsUpdateTime.tv_sec);
    }

    /* clear gps speed */
    if (now.tv_sec > mGpsUpdateTime.tv_sec + 30) {
        mLbs.speed_x10 = 0;
    }

    mSyncMsg.vehicle_io = vehicle_io;
    if (!vehicle_io.acc) {
        /* acc关闭时GPS速度必定为0 */
        mLbs.speed_x10 = 0;
    }
    mSyncMsg.dvr_io = dvr_io;
    mSyncMsg.sys = mSys;
    mSyncMsg.pwr_set = mPwrSet;
    mSyncMsg.lbs = mLbs;

    mSyncMsg.pulses_speed_x10 = mPulses_speed_x10;
    mSyncMsg.pulses_turnl = mPulses_turnl;
    mSyncMsg.pulses_turnr = mPulses_turnr;

    mSyncMsg.can_speed_x10 = mCan_speed_x10;
    mSyncMsg.can_turnl = mCan_turnl;
    mSyncMsg.can_turnr = mCan_turnr;

    mSyncMsg.speed_x10 = mSpeed_x10;
    mSyncMsg.turnl = mTurnl;
    mSyncMsg.turnr = mTurnr;

    mSyncMsg.total_mileage = (mCan.isEnable(CS_MILE_TOTAL)) ? mCan.mCarInfo.mileTotal : mTotal_mileage;
    get_net_status(&mSyncMsg.csq[0], &mSyncMsg.net_info[0]);
	get_iccid(mSyncMsg.iccid);
	get_imei(mSyncMsg.imei);
    memcpy(mSyncMsg.adcRaw, mAdcRaw, sizeof(mAdcRaw));
    memcpy(mSyncMsg.adcVol, mAdcVoltage, sizeof(mAdcVoltage));
    memcpy(mSyncMsg.key, mKey, sizeof(mKey));
    mSyncMsg.accOff_event = mAccOffEvent;
    broadcast_msg(MCU_MSG_TYPE_STAT, (const char*)&mSyncMsg, sizeof(mSyncMsg));
}

void IOMsgHandler::broadcast_msg(McuMsgTypeE cmd, const char *msg, uint32_t len)
{
    uint32_t remain = len;
    uint32_t offset = 0;
    uint8_t buf[MCU_MSG_MAX_SIZE];
    McuMessage *pMsg = (McuMessage *)&buf[0];

    do {
        pMsg->ipcMsg.len = remain <= MCU_MSG_PAYLOAD_MAX_SIZE ? remain : MCU_MSG_PAYLOAD_MAX_SIZE;
        pMsg->ipcMsg.type = cmd;
        memcpy(pMsg->u.u8Array, &msg[offset], pMsg->ipcMsg.len);
        mcuAgent->send(&(pMsg->ipcMsg));
        offset += pMsg->ipcMsg.len;
        remain = remain - pMsg->ipcMsg.len;
    } while(remain >0);
}

void IOMsgHandler::lcd_bg(struct timespec& now)
{
    if(!mKeyLast && mKey[0]) {
        clock_gettime(CLOCK_MONOTONIC, &mKeyHitTime);
        mKeyHitEvent = true;
    }
    mKeyLast = mKey[0];

    if(mKeyHitEvent && mPwrSet.sys_pwr) { /* 在非低功耗模式下，响应按键 */
        if(!mLedaOnDone) {
            logi("led backlight on\n");
            IOMsg msg(MAJOR_GPIO, LCM_LEDA_ON);
            send(msg);
            mKey[0] = KEY_NONE; /*第一次按下仅亮起来，不切屏幕*/
            mLedaOnDone = true;
            mLedaOffDone= false;
        }
    } else if (!mKeyHitEvent || !mPwrSet.sys_pwr){ /* 按键事件消失或者低进入功耗模式熄屏 */
        if(!mLedaOffDone) {
            logi("led backlight off\n");
            IOMsg msg(MAJOR_GPIO, LCM_LEDA_OFF);
            send(msg);
            mLedaOffDone= true;
            mLedaOnDone = false;
        }
    }

    /* clear keyhit */
    if(mKeyHitEvent && now.tv_sec > mKeyHitTime.tv_sec + mLcdBackLightTime) {
        mKeyHitEvent = false;
    }
}

bool pwrkeyRebootIsMark(void)
{
    char propValue[PROP_VALUE_MAX];
    memset(propValue, 0, sizeof(propValue));

    __system_property_get(PROP_PERSIST_MINIEYE_PWRKEYREBOOT, propValue);

    const char *p = propValue;
    if(!strcmp(p, "done")) {
        return true;
    }
    return false;
}

void pwrkeyRebootMark(void)
{
    char propValue[PROP_VALUE_MAX];
    snprintf(propValue, sizeof(propValue), "done");
    __system_property_set(PROP_PERSIST_MINIEYE_PWRKEYREBOOT, propValue);
}

void pwrkeyRebootMarkClear(void)
{
    char propValue[PROP_VALUE_MAX];
    snprintf(propValue, sizeof(propValue), "undo");
    __system_property_set(PROP_PERSIST_MINIEYE_PWRKEYREBOOT, propValue);
}

void IOMsgHandler::get_work_mode(void)
{
    static WorkModeE last_mode = WORK_MODE_MAX;
    char propValue[PROP_VALUE_MAX] = {0};

    memset(propValue, 0, sizeof(propValue));

    int len = __system_property_get(PROP_RW_MINIEYE_WORK_MODE, propValue);
    if(len == 0) {
        __system_property_set(PROP_RW_MINIEYE_WORK_MODE, "ft");
    }

    const char *p = propValue;
    if (!strcmp(p, "ft")) {
        mMode = WORK_MODE_FT;
    } else if(!strcmp(p, "aging")) {
        mMode = WORK_MODE_AGING;
    } else if(!strcmp(p, "setup")) {
        mMode = WORK_MODE_SETUP;
    } else if(!strcmp(p, "normal")) {
        mMode = WORK_MODE_NORMAL;
    } else if(!strcmp(p, "sample")) {
        mMode = WORK_MODE_SAMPLE;
    } else {
        mMode = WORK_MODE_FT;
    }

    /* 模式切换 */
    if (last_mode != mMode) {
        mSocRebootEnable = true;
        mLcdBackLightTime = get_backlight();
        logd("get backlight time %u second", mLcdBackLightTime);
        if (mMode == WORK_MODE_AGING) {
            mSocRebootEnable = false;       /* 老化模式关闭重启机制 */
            mLcdBackLightTime = 0xFFFFFFFF; /* 老化模式LCD背光常亮 */
            logd("aging mode set backlight time 0x%x second", mLcdBackLightTime);
        } else {
        }

        logi("work mode enter: %s\n", work_mode_name[mMode]);
    }
    last_mode = mMode;
}

void IOMsgHandler::acc_monitor(void)
{
    static uint32_t acc_off_cnt = 0;
    static uint32_t acc_on_cnt = 0;
    
    static bool sysPwrOffDone = false;
    struct timespec now;
    CalGpsMiles & calGpsMiles = CalGpsMiles::getInstance();

    //logd("acc status %d", mAccStatus);
    switch (mAccStatus) {
        case E_ACC_INVAILD : {
            if (vehicle_io.acc) {
                mAccStatus = E_ACC_ON_ING;
            } else {
                mAccStatus = E_ACC_OFF_ING;
            }
            break;
        }

        case E_ACC_ON_ING : {
            // acc on计数
            if (vehicle_io.acc) {
                acc_on_cnt++;
            } else {
                acc_off_cnt = 0;
                acc_on_cnt = 0;
                mAccStatus = E_ACC_OFF_ING;
                break;
            }

            // 状态切换
            if (acc_on_cnt > 5) {
                sysPwrOffDone = false;
                mAccOffEvent = false;
                mPwrKeyRstDone = false;
                MY_SPINLOCK_X(mPwrlock);
                mPwrDn = 0;
                pwrkeyRebootMarkClear();
                mLogger->mlog("acc on event");

                IOMsg msg(MAJOR_GPIO, GPIO_MINOR_BAT_ON);
                send(msg);
                logi("send bat pwr on cmd\n");

                /*ACC开 唤醒背光*/
                mLedaOffDone= true;
                mLedaOnDone = false;
                clock_gettime(CLOCK_MONOTONIC, &mKeyHitTime);
                mKeyHitEvent = true;

                mAccOffEvent = false;
                calGpsMiles.setAccStatus(true);
            
                mAccStatus = E_ACC_ON;
                mLogger->mlog("acc on event");
            }
            break;
        }

        case E_ACC_ON : {

            if (!vehicle_io.acc) {
                acc_off_cnt = 0;
                acc_on_cnt = 0;
                mAccStatus = E_ACC_OFF_ING;
            }
            
            break;
        }

        case E_ACC_OFF_ING : {
            if (!vehicle_io.acc) {
                acc_off_cnt++;
            } else {
                acc_off_cnt = 0;
                acc_on_cnt = 0;
                mAccStatus = E_ACC_ON_ING;
                break;
            }

            // 状态切换
            if (acc_off_cnt > 5) {
                MY_SPINLOCK_X(mPwrlock);
                clock_gettime(CLOCK_MONOTONIC, &mPwrDnpdingTime);
                mAccStatus = E_ACC_OFF;

                mAccOffEvent = true;
                calGpsMiles.setAccStatus(false);
                
                mLogger->mlog("acc off event");
            }
            
            break;
        }
        
        case E_ACC_OFF : {
            if (vehicle_io.acc) {
                acc_off_cnt = 0;
                acc_on_cnt = 0;
                mAccStatus = E_ACC_ON_ING;
            }
            break;
        }
        
    }

#if 0
    if (!vehicle_io.acc && acc_off_cnt++ > 5 && !mAccOffEvent) { /* ACC ON ->ACC OFF */
        logi("ACC OFF event\n");
        calGpsMiles.setAccStatus(false);
        acc_off_cnt = 0;
        acc_on_cnt = 0;
        mAccOffEvent = true;
        MY_SPINLOCK_X(mPwrlock);
        clock_gettime(CLOCK_MONOTONIC, &mPwrDnpdingTime);
        mLogger->mlog("acc off event");
    }

    if (vehicle_io.acc && acc_on_cnt++ > 5 && mAccOffEvent) { /* ACC OFF -> ACC ON */
        logi("ACC ON event\n");
        calGpsMiles.setAccStatus(true);
        acc_on_cnt = 0;
        acc_off_cnt = 0;
        sysPwrOffDone = false;
        mAccOffEvent = false;
        mPwrKeyRstDone = false;
        MY_SPINLOCK_X(mPwrlock);
        mPwrDn = 0;
        pwrkeyRebootMarkClear();
        mLogger->mlog("acc on event");

        IOMsg msg(MAJOR_GPIO, GPIO_MINOR_BAT_ON);
        send(msg);
        logi("send bat pwr on cmd\n");

        /*ACC开 唤醒背光*/
        mLedaOffDone= true;
        mLedaOnDone = false;
        clock_gettime(CLOCK_MONOTONIC, &mKeyHitTime);
        mKeyHitEvent = true;
    }
#endif

    clock_gettime(CLOCK_MONOTONIC, &now);

    /* 根据ACC状态关机 */
    if (mPwrSet.sys_pwr && mAccOffEvent && !sysPwrOffDone) {
        bool ret = tryPwrOff(now);
        if (ret) { /* 进入低功耗模式后，清除IC卡状态 */
            mIcInsertDone = false;
            mICCardInsertValid = false;
            clearIccardBuf();
            mSysOffTime.tv_sec = now.tv_sec; /* 进入低功耗的时间 */
        }
    } else if (!mPwrSet.sys_pwr && mAccOffEvent) { /* 确定syspwr 断电*/
        /* mcu返回的sysPwr状态*/
        sysPwrOffDone = true;
    }

    if (now.tv_sec > mHeartBeatTime.tv_sec + 5) { /*5秒发送一次心跳*/
        IOMsg msg(MAJOR_HEART_BEAT, 0);
        send(msg);
        mHeartBeatTime = now;
    }

    if(mSocRebootEnable) {
        /* 进入低功耗模式10分钟后复位一次msm8953*/
#define MSM8953_RST_TIME (600)
        if(!mPwrSet.sys_pwr && now.tv_sec > mSysOffTime.tv_sec + MSM8953_RST_TIME) {
            if (!mPwrKeyRstDone && !pwrkeyRebootIsMark()) {
                pwrkeyRebootMark();

#if 0
                mLogger->mlog("reboot system");
                sync();
                sync();
                system("reboot");
#else
                mLogger->mlog("send cmd reset msm8953");
                sync();
                sync();
                logi("send power key reset cmd\n");
                IOMsg msg(MAJOR_PWRKEY_RST, 0);
                send(msg);
#endif
                mPwrKeyRstDone = true;
            }
        }
    }

    lcd_bg(now);
}

void IOMsgHandler::tryPrint(void)
{
    if (!vehicle_io.acc || dvr_io.printer_no_paper) { /*ACC off 不打印*/
        logi("printer don't work when acc off!\n");
        return;
    }

    if (mMode == WORK_MODE_NORMAL && mSpeed_x10) { /*有速度时不打印*/
        logi("printer don't work when speed is not zero!\n");
        return;
    }

    if (mPrint.IsBusy()) {
        logi("printer is busying!\n");
        return;
    }
    logi("printer is request!\n");
    set_print_info();
    IOMsg msg(MAJOR_PRINTER, PRINTER_MINOR_PRINT);
    send(msg);
    mPrint.setBusy();
    clock_gettime(CLOCK_MONOTONIC, &mPrinterTime);//打印时间
}

void IOMsgHandler::printLostContinue(void)
{
    if(mPrint.IsBusy()) { /*打印获取ACK超时重发*/
        struct timespec now;
        clock_gettime(CLOCK_MONOTONIC, &now);//打印时间
        if(now.tv_sec > mPrinterTime.tv_sec + 4) {
            mPrinterTime = now;
            string dot = mPrint.get_oneline();
            logd("timeout printer repeat data len %d\n", dot.size());

            if (dot.size() == 0) { /*打印结束*/
                IOMsg msg(MAJOR_PRINTER, PRINTER_MINOR_ROLL); /*留白*/
                send(msg);
            } else { /*持续打印， 丢包重发*/
                IOMsg msg(MAJOR_PRINTER, PRINTER_MINOR_PRINT);
                msg.data.append((const char *)dot.c_str(), dot.size());
                send(msg);
            }
        }
    }
}

bool IOMsgHandler::iccardInfoCheckOk(const uint8_t *buf, int len)
{
    if (len < 1 ) {
        return false;
    }

    uint8_t crc = buf[0];
    for (int32_t i=1; i<len-1; i++) {
        crc ^= buf[i];
    }

    logd("crc 0x%02x cal_crc 0x%02x\n", buf[len-1], crc);
    return (crc == buf[len-1]);
}

void IOMsgHandler::handleIccardReadAck(uint8_t minor, const uint8_t *data, int size)
{
    uint8_t buf[MCU_MSG_MAX_SIZE] = {0};

    if (size != ICCARD_SIZE_MAX) {
        loge("iccard read size error (%d)\n", size);
        return;
    }

    uint8_t dst1[ICCARD_SIZE_MAX];
    uint8_t dst2[ICCARD_SIZE_MAX];
    memset(dst1, 0x0, ICCARD_SIZE_MAX);
    memset(dst2, 0xFF, ICCARD_SIZE_MAX);

    if (!memcmp(data, dst1, ICCARD_SIZE_MAX) || \
            !memcmp(data, dst2, ICCARD_SIZE_MAX)) {
        logi("Iccard is new card!");
        goto out;
    }

    if (!iccardInfoCheckOk(data, size)) {
        loge("Iccard read, info crc error!");
        goto out;
    }

    /* 如果是发送IC卡消息， 需判断当前插卡是否有效再更新Buf */
    if (mICCardInsertValid) { /*插卡有效, 更新buf*/
        mICCardidOk= 1;
        memcpy(&iccard_wr, data, sizeof(ICCardWrBuf));
        logd("mICCardidOk %d\n", mICCardidOk);

        char y, m, d, *p=&iccard_wr.expiry_bcd[0];
        if (mICCardidOk) {
            from_bcd1(y, p);
            from_bcd1(m, p);
            from_bcd1(d, p);
            expiry = (2000 + y) * 1000 + m * 100 + d;
        }

        /* sync jt808 */
        McuMessage *pMsg = (McuMessage *)&buf[0];
        pMsg->ipcMsg.len = MCU_MSG_SIZE_ICCARD;
        pMsg->ipcMsg.type = MCU_MSG_TYPE_IC_CARD;
        memcpy(pMsg->u.u8Array, data, size);
        mcuAgent->send(&(pMsg->ipcMsg));

        pMsg->ipcMsg.type = MCU_MSG_TYPE_IC_CARD_LOGIN;
        mcuAgent->send(&(pMsg->ipcMsg));
    }

out:
    /* 无论插卡是否有效，都可以通过指令读出卡 */
    McuMessage *p = (McuMessage *)&buf[0];
    p->ipcMsg.len = size;
    p->ipcMsg.type = MCU_MSG_TYPE_IC_CARD_RD;
    memcpy(p->u.u8Array, data, size);
    mcuAgent->send(&(p->ipcMsg));
}

void IOMsgHandler::handleIccardAck(uint8_t minor, const char *data, int size)
{
    static char iccard_retry_cnt = 10;
    switch(minor) {
        case ICCARD_MINOR_WRITE_OK:
            logd("iccard write ack ok");
            break;
        case ICCARD_MINOR_WRITE_FAIL:
            logd("iccard write ack fail");
            break;
        case ICCARD_MINOR_READ_FAIL:
            {
                if (iccard_retry_cnt-- <= 0) {
                    mIcReadRetry = false;
                    iccard_retry_cnt = 10;
                    logd("iccard ccard_retry_cnt reset to 10");
                } else {
                    mIcReadRetry = true;
                    logd("iccard read ack fail, retry cnt %d", iccard_retry_cnt);
                }
            }
            break;
        case ICCARD_MINOR_READ_OK:
            {
                mIcReadRetry = false;
                iccard_retry_cnt = 10;
                handleIccardReadAck(minor, (const uint8_t *)data, size);
            }
            break;
        default:
            break;
    }
}
void IOMsgHandler::clearIccardBuf(void)
{
    memset(&iccard_wr, 0, sizeof(ICCardWrBuf));
    mICCardidOk = 0;
}

bool IOMsgHandler::iccardPlayAudioIsMark(void)
{
    char propValue[PROP_VALUE_MAX];
    memset(propValue, 0, sizeof(propValue));

    __system_property_get(PROP_RW_MINIEYE_ICCARDAUDIO, propValue);

    const char *p = propValue;
    if(!strcmp(p, "done")) {
        return true;
    }

    return false;
}

void IOMsgHandler::iccardPlayAudioMark(void)
{
    char propValue[PROP_VALUE_MAX];
    snprintf(propValue, sizeof(propValue), "done");
    __system_property_set(PROP_RW_MINIEYE_ICCARDAUDIO, propValue);
}

void IOMsgHandler::iccardPlayAudioMarkClear(void)
{
    char propValue[PROP_VALUE_MAX];
    snprintf(propValue, sizeof(propValue), "undo");
    __system_property_set(PROP_RW_MINIEYE_ICCARDAUDIO, propValue);
}

void IOMsgHandler::autoReadIccard(void)
{
    static int32_t inserted_cnt;
    static int32_t pull_out_cnt;

    if (!vehicle_io.acc) {
        return;
    }

    if (!mIcInsertDone && dvr_io.iccard_inserted) {
        if (++inserted_cnt >= 5) { /*inserted*/
            mIcInsertDone = true;
            mICCardPullOutValid = false;
            clock_gettime(CLOCK_MONOTONIC, &insertTime);
            inserted_cnt = 0;
            logi("iccard inserted.\n");
            if (mSpeed_x10) { /* 有车速不读卡 */
                mICCardInsertValid = false; /* 插卡无效 */
                logi("can't read iccard when speed is not zero!\n");
            } else {
                logi("iccard insert ok!\n");
                mICCardInsertValid = true;
                request_iccard();
            }
        }
    } else if (mIcInsertDone && !dvr_io.iccard_inserted) {
        if (++pull_out_cnt >= 5) {
            /* 拔卡后再插卡都需要重新播报 */
            iccardPlayAudioMarkClear();
            //mICCardInsertValid = false;
            mIcInsertDone = false;
            pull_out_cnt = 0;
            if (mSpeed_x10) { /* 有车速不能拔出卡 */
                logi("pull out iccard invalid when speed is not zero!\n");
                mICCardPullOutValid = false;
            } else {
                mICCardPullOutValid = true;
                //memset(&iccard_wr, 0, sizeof(ICCardWrBuf));
                if(mICCardidOk && mICCardPullOutValid) { /* 合法的拔卡 */
                    uint8_t buf[MCU_MSG_MAX_SIZE] = {0};
                    McuMessage *p = (McuMessage *)&buf[0];
                    p->ipcMsg.len = 0;
                    p->ipcMsg.type = MCU_MSG_TYPE_IC_CARD_LOGOUT;
                    mcuAgent->send(&(p->ipcMsg));
                }
            }
            logi("iccard pull out.\n");
        }
    }
}

void IOMsgHandler::sendCommand(McuMsgTypeE cmd, const uint8_t *data, uint32_t size)
{
    uint8_t buf[MCU_MSG_MAX_SIZE] = {0};
    McuMessage *pMsg = (McuMessage *)&buf[0];
    pMsg->ipcMsg.len = size;
    pMsg->ipcMsg.type = cmd;
    if (size > 0) {
        memcpy(pMsg->u.u8Array, data, size);
    }
    mcuAgent->send(&(pMsg->ipcMsg));
}

#define ADC_PWR_LOW_VAL (10.0)
#define ADC_PWR_OFF_VAL (8.9)
#define ADC_BAT_ABSENT_VAL (6.0)

void IOMsgHandler::sys_pwr_monitor(void)
{
    if (mSys.power_off) { /* 掉电后关闭欠压状态 */
        mSys.power_low = 0;
    } else {
        if (mAdcVoltage[ADC_CHN_24V] < ADC_PWR_LOW_VAL ||\
                (mAdcVoltage[ADC_CHN_24V] > 14.0 && mAdcVoltage[ADC_CHN_24V] < 18.0)) {
            if (!mSys.power_low) {
                mSys.power_low = 1;
                logi("main power low(%.2fv) enter", mAdcVoltage[ADC_CHN_24V]);
                mLogger->mlog("main power low(%.2fv) enter", mAdcVoltage[ADC_CHN_24V]);
            }
        } else {
            if (mSys.power_low) {
                logi("main power low(%.2fv) exit", mAdcVoltage[ADC_CHN_24V]);
                mLogger->mlog("main power low(%.2fv) exit", mAdcVoltage[ADC_CHN_24V]);
                mSys.power_low = 0;
            }
        }
    }

    /* 主电源掉电判断 */
    if (!vehicle_io.acc && mAdcVoltage[ADC_CHN_24V] < ADC_PWR_OFF_VAL) {
        if(!mSys.power_off) {
            mSys.power_off = 1;
            logi("main power off(%.2fv) enter", mAdcVoltage[ADC_CHN_24V]);
            mLogger->mlog("main power off(%.2fv) enter", mAdcVoltage[ADC_CHN_24V]);
            // 针对B1版本之后的机器，电池电压可以用来判断电池是否存在，超级电容电压也可以用来判断
            //
            conf_t& sys = Manager::getInstance().sys;
            if (sys.boardType >= IDVR_BOARD_TYPE::B1) {
                if (mAdcVoltage[ADC_CHN_BAT] <= ADC_BAT_ABSENT_VAL) { // 没有电池（只有超级电容）
                    mLogger->mlog("main power off bat(%.2fv) cap(%.2fv)", mAdcVoltage[ADC_CHN_BAT], mAdcVoltage[ADC_CHN_CAP]);
                    // 关闭12V电源
                    IOMsg msg(MAJOR_GPIO, PWR_12V_OFF); // 12V在MCU会随ACC打开
                    send(msg);
                }
            }
        }
    } else {
        if(mSys.power_off) {
            mSys.power_off = 0;
            logi("main power off(%.2fv) exit", mAdcVoltage[ADC_CHN_24V]);
            mLogger->mlog("main power off(%.2fv) exit", mAdcVoltage[ADC_CHN_24V]);
        }
    }

    /*进入低功耗模式后，且外部电源掉电, 关闭电池*/
    if (mPwrSet.bat_discharging && !mPwrSet.sys_pwr && mSys.power_off) {
        logi("dcin(%.1f) off, send bat-off\n", mAdcVoltage[ADC_CHN_24V]); /* 网口adb日志无法输出，应该网卡已经断电 */
        mLogger->mlog("dcin(%.1f) off, send bat-off", mAdcVoltage[ADC_CHN_24V]);
        sync();
        IOMsg msg(MAJOR_GPIO, GPIO_MINOR_BAT_OFF);
        send(msg);
    }
}

int32_t run_ft_test(const char *cmd)
{
    char line[2048];
    memset(line, 0, sizeof(line));
    FILE *fp = popen(cmd, "r");
    if (!fp){
        return -1;
    }
    while (fgets(line, sizeof(line), fp) != NULL) {
        logd("read : %s", line);
    }
    pclose(fp);
    if(NULL != strstr(line, "TEST OK")){
        return 0;
    }
    return -2;
}

void IOMsgHandler::GB19056Update(void)
{
    my::uint64 now = Manager::getInstance().utc_milliseconds();
    my::uint now_sec = (my::uint)(now / 1000);
    my::uint now_ms = (my::uint)(now % 1000);

#if 0
    static my::uint64 last = now;
    static float total = 0;
    my::uint64 gap = now - last;
    float delta_mileage = 1.0*(gap * mSpeed_x10)/(36000 * 1000); /*100ms 增加的公里数*/
    total += delta_mileage;
    //logd("gap %lu delta_mileage %f total %f", gap, delta_mileage, total);
    last = now;
#endif
    // 时间大于2011年1月1日才认为校时成功
    if (now < (1293811200LL * 1000)) {
        logd("waiting for time sync!", now);
        return;
    }
    mTripSyncSec = my::timestamp::now();

    logs.update_speed_status_log(now_sec, mLbs.speed_x10, mSpeed_x10);
    LoginIndex idx;
    if(mICCardidOk) {
        idx = logs.update_login_log(now_sec, dvr_io.iccard_inserted, mICCardidOk, iccard_wr.id, iccard_wr.cert, expiry);
    }

    /* GB19056的IO顺序，左右转向反了 */
    IOStatus gb19056_io = vehicle_io;
    gb19056_io.left_turn = vehicle_io.right_turn;
    gb19056_io.right_turn = vehicle_io.left_turn;
    gb19056_io.iccard_inserted = dvr_io.iccard_inserted;

    mTotal_mileage = records.update(now_sec, now_ms, mLbs, idx, mSpeed_x10, gb19056_io);
    logs.update(now_sec, now_ms, mLbs.status, mLbs.lat_x1kw, mLbs.lng_x1kw, mLbs.alt_x10, iccard_wr.id, mSpeed_x10, mTotal_mileage, gb19056_io);
}

void IOMsgHandler::proc(my::uchar major, my::uchar minor, const char* data1, int size1)
{
    CalGpsMiles & calGpsMiles = CalGpsMiles::getInstance();

	switch (major)
	{
	case MAJOR_HOST: // 状态上报
		{
	        LBS lbs; /* old gps info, unuse*/
			tmp.msg.capacity(size1);
			tmp.msg.assign(data1, size1);
			my::constr data(tmp.msg);

			my::uint lbs_clk;
			int delta_pulses;
            uint8_t gps_status;
            uint32_t mcu_tick=0;
            uint16_t tempRaw=0;
            uint32_t resv[5];
            uint16_t resv16[5];
            uint16_t frame_crc_err=0, frame_len_err=0;


	        //logd("[IOMsgHandler::send] %s\n", my::hex(my::constr(data1, size1)).c_str());

			data >> my::ntoh
                 >> gps_status >> lbs_clk >> lbs.lat_x1kw >> lbs.lng_x1kw >> lbs.alt_x10 >> lbs.speed_x10 >> lbs.dir_x100
				 >> mSpeed_pulses_fre >> delta_pulses
				 >> mKey[0] >> mKey[1]
				 >> vehicle_io.value >> dvr_io.value
                 >> mAdcRaw[ADC_CHN_KEY] >> mAdcRaw[ADC_CHN_24V]
                 >> mAdcRaw[ADC_CHN_BAT] >> mAdcRaw[ADC_CHN_EXT_VAL1] >> mAdcRaw[ADC_CHN_EXT_VAL2]
                 >> mcu_tick >> mMcuTempture_x100 >> tempRaw
                 >> frame_crc_err >> frame_len_err
                 >> mTurnl_pulses_fre >> mTurnr_pulses_fre >> mPwrSet.val >> resv16[0] >> mAdcRaw[ADC_CHN_CAP] >> mAdcRaw[ADC_CHN_TEM];
            if (!access("/data/fake_printer_hot", R_OK)) {
                dvr_io.printer_hot = 1;
            }

            /* 初始化 */
            vehicle_io.can_signal_loss = 0;

            lbs.status = gps_status >> 4;
            lbs.antenna = gps_status & 0x0F;
			lbs.time = lbs_clk;
            /* libs 没有使用 */

            mMcuUptime = mcu_tick/1000;
            auto as = mCan.mConfig->mConfig.analog_cfg.analogSignals[AS_IO_HI_BEAM];
            if (as.enable && !as.polarity) {
                vehicle_io.far_light = !vehicle_io.far_light;
            }
            as = mCan.mConfig->mConfig.analog_cfg.analogSignals[AS_IO_LO_BEAM];
            if (as.enable && !as.polarity) {
                vehicle_io.normal_light = !vehicle_io.normal_light;
            }
            as = mCan.mConfig->mConfig.analog_cfg.analogSignals[AS_IO_BRAKE];
            if (as.enable && !as.polarity) {
                vehicle_io.brake = !vehicle_io.brake;
            }
            as = mCan.mConfig->mConfig.analog_cfg.analogSignals[AS_IO_TURNLAMP];
            if (as.enable && !as.polarity) {
                vehicle_io.left_turn = !vehicle_io.left_turn;
                vehicle_io.right_turn= !vehicle_io.right_turn;
            }
            as = mCan.mConfig->mConfig.analog_cfg.analogSignals[AS_IO_DOOR];
            if (as.enable && !as.polarity) {
                vehicle_io.door = !vehicle_io.door;
            }
            as = mCan.mConfig->mConfig.analog_cfg.analogSignals[AS_IO_SOS];
            if (as.enable && !as.polarity) {
                vehicle_io.emergency_alarm = !vehicle_io.emergency_alarm;
            }
            mPulses_fre = (mSpeed_pulses_fre ? 1.0*64000U/mSpeed_pulses_fre : 0);

            mPulses_turnl = vehicle_io.left_turn;
            mPulses_turnr = vehicle_io.right_turn;

            float turnl = mTurnl_pulses_fre ? 64000U/mTurnl_pulses_fre: 0;
            float turnr = mTurnr_pulses_fre ? 64000U/mTurnr_pulses_fre: 0;
            //logd("[raw fre] speed %d left %d right %d\n", mSpeed_pulses_fre,  mTurnl_pulses_fre, mTurnr_pulses_fre);

            handleAspeed(mPulses_fre);
            set_speed();

            GB19056Update();

            /* 发在刷菜单之前， 当息屏唤醒后，一个按键忽略 */
            acc_monitor(); /*do before status sync*/

			// 刷新菜单
            gui_flush(mKey[0]);

            mAdcVoltage[ADC_CHN_KEY] = mAdcRaw[ADC_CHN_24V] * 0;
            mAdcVoltage[ADC_CHN_24V] = mAdcRaw[ADC_CHN_24V] * 3.3 * (1069.8) / (69.8 * 4096);

            if (mPwrSet.sys_pwr && !mSys.power_off) { /* 不在低功耗模式，电流很大，需要补偿 */
                mAdcVoltage[ADC_CHN_24V] = mAdcVoltage[ADC_CHN_24V] + 0.2 + (mAdcVoltage[ADC_CHN_24V] * 0.2)/12; /* 补偿0.2-0.4 */
            }

            mAdcVoltage[ADC_CHN_BAT] = mAdcRaw[ADC_CHN_BAT] * 389 * 3.3 / (59 * 4096);

            mAdcVoltage[ADC_CHN_EXT_VAL1] = mAdcRaw[ADC_CHN_EXT_VAL1] * 3.3 * 15.3266 / 4096;
            as = mCan.mConfig->mConfig.analog_cfg.analogSignals[AS_IO_AD1];
            if (as.enable && !as.polarity) {
                if (mAdcVoltage[ADC_CHN_EXT_VAL1] >= 1) {
                    mAdcVoltage[ADC_CHN_EXT_VAL1] = 0;
                } else {
                    mAdcVoltage[ADC_CHN_EXT_VAL1] += 12;
                }
            }
            mAdcVoltage[ADC_CHN_EXT_VAL2] = mAdcRaw[ADC_CHN_EXT_VAL2] * 3.3 * 15.3266 / 4096;
            as = mCan.mConfig->mConfig.analog_cfg.analogSignals[AS_IO_AD2];
            if (as.enable && !as.polarity) {
                if (mAdcVoltage[ADC_CHN_EXT_VAL2] >= 1) {
                    mAdcVoltage[ADC_CHN_EXT_VAL2] = 0;
                } else {
                    mAdcVoltage[ADC_CHN_EXT_VAL2] += 12;
                }

            }
            mAdcVoltage[ADC_CHN_CAP] = mAdcRaw[ADC_CHN_CAP] * 3.3 * 2.941 / 4096;
            mAdcVoltage[ADC_CHN_TEM] = mAdcRaw[ADC_CHN_TEM] * 0; // TODO


            bool trip_alert = false;
            static int16_t last_tempture_x100;
            char logbuf[2048];
            uint32_t start, end;
            char idx;
            logs.curent_trip_dump(&idx, &start, &end);
            if (clk.elapsed() >= 1000) {
                std::vector<LongTrip> res;
                my::uint dayEnd = Manager::getInstance().utc_seconds();
                my::uint back24h = dayEnd - 24 *3600;
                my::uint dayBgn = dayEnd - Manager::getInstance().day_seconds();
                my::uint todayTotal = 0;
                logs.query_trip_logs(res, back24h, dayEnd, 2000);
                logd("dayBgn = %d, %d", dayBgn, back24h);
                auto it = res.begin();
                while (it != res.end()) {
                    my::datetime tick1, tick2;
                    const uint8_t *p = it->start_bcd_time;
                    bcd2datetime(tick1, p);
                    p = it->end_bcd_time;
                    bcd2datetime(tick2, p);
                    my::uint diff = 0;
                    if (access("/data/24h_drv_time", R_OK)) {
                        if (((tick1.to_utc_seconds() - TIME_ZONE_OFFSET) < dayBgn) &&
                            (dayBgn <= (tick2.to_utc_seconds() - TIME_ZONE_OFFSET))) {
                            diff = tick2.to_utc_seconds() - TIME_ZONE_OFFSET - dayBgn;
                        } else if (dayBgn <= (tick1.to_utc_seconds() - TIME_ZONE_OFFSET)) {
                            diff = tick2.to_utc_seconds() - tick1.to_utc_seconds();
                        }
                    } else {
                        if (((tick1.to_utc_seconds() - TIME_ZONE_OFFSET) < back24h) &&
                            (back24h <= (tick2.to_utc_seconds() - TIME_ZONE_OFFSET))) {
                            diff = tick2.to_utc_seconds() - TIME_ZONE_OFFSET - back24h;
                        } else {
                            diff = tick2.to_utc_seconds() - tick1.to_utc_seconds();
                        }
                    }
                    todayTotal += diff;
                    logd(">[%d ~ %d] = %d", tick1.to_utc_seconds() - TIME_ZONE_OFFSET, tick2.to_utc_seconds() - TIME_ZONE_OFFSET, diff);
                    it++;
                }
                logd("todayTotal = %d, cur [%d ~ %d]", todayTotal, start, end);
                cur_trip_info_sync(start, end, todayTotal);

                if (mSpeed_x10 && end > start + 4*3600) {
                    trip_alert = true;
                } else {
                    trip_alert = false;
                }
                clk = my::timestamp::now();
                set_power_vol();
                get_work_mode();
            }

			if (mLogVerbose[LOG_TYPE_STATUS] > 0 && status_log_clk.elapsed() >= 1000)
			{
				// 打印日志

				snprintf(logbuf, sizeof(logbuf), "key[%d-%d] gps_time[%u] mLbs[%f, %f, %.1f] sat %d located %d dir %.1f antenna %d speed[%.1f, %.1f] mileage %.2f acc %d alarm %d back %d near %d far %d "
				        "left %d right %d brake %d door %d can_loss %d hot %d nopaper %d panel %d inserted %d IdOK %d ID %.18s logInIdx %d start %d end %d"
                        " adc[%d(%.2fv)-%d(%.2fv)-%d(%.2fv)-%d(%.2fv)-%d(%.2fv)-%d(%.2fv)] mcuUpTime[%s] boardTempture %d temptureRaw 0x%04x frame_crc_err %d "
                        "sys_pwr %d(0x%04x) pwr_low %d pwr_off %d fmcu_rst_soc %d fauto_ctrl_cap %d fauto_ctrl_bat %d ",
					mKey[0], mKey[1], mLbs.time,
					mLbs.lng_x1kw / 10000000.0, mLbs.lat_x1kw / 10000000.0, mLbs.alt_x10 / 10.0,
					mLbs.sat, mLbs.status, mLbs.dir_x100 / 100.0, mLbs.antenna,
					mSpeed_x10 / 10.0, mLbs.speed_x10 / 10.0,
					mTotal_mileage,
					vehicle_io.acc, vehicle_io.emergency_alarm, vehicle_io.backwards, vehicle_io.normal_light,
					vehicle_io.far_light, vehicle_io.left_turn, vehicle_io.right_turn, vehicle_io.brake, vehicle_io.door, vehicle_io.can_signal_loss,
					dvr_io.printer_hot, dvr_io.printer_no_paper, dvr_io.panel_door_open, dvr_io.iccard_inserted,
					mICCardidOk,
				    iccard_wr.id,
                    idx,
                    start,
                    end,
                    mAdcRaw[ADC_CHN_24V],
                    mAdcVoltage[ADC_CHN_24V],
                    mAdcRaw[ADC_CHN_BAT],
                    mAdcVoltage[ADC_CHN_BAT],
                    mAdcRaw[ADC_CHN_EXT_VAL1],
                    mAdcVoltage[ADC_CHN_EXT_VAL1],
                    mAdcRaw[ADC_CHN_EXT_VAL2],
                    mAdcVoltage[ADC_CHN_EXT_VAL2],
                    mAdcRaw[ADC_CHN_CAP],
                    mAdcVoltage[ADC_CHN_CAP],
                    mAdcRaw[ADC_CHN_TEM],
                    mAdcVoltage[ADC_CHN_TEM],
                    uptime(mMcuUptime).c_str(),
                    mMcuTempture_x100,
                    tempRaw,
                    frame_crc_err,
                    mPwrSet.sys_pwr,
                    mPwrSet.val,
                    mSys.power_low,
                    mSys.power_off,
                    mPwrSet.fmcu_rst_soc,
                    mPwrSet.fauto_ctrl_cap,
                    mPwrSet.fauto_ctrl_bat);
                logd("%s", logbuf);
                status_log_clk = my::timestamp::now();

            }
			if (trip_alert && trigger_clk.elapsed() >= 5000) /* 5 min*/
            {
				trigger_clk = my::timestamp::now();
                logd("trip trigger!\n");
                // do trigger...
                send_trip_alert();
            }

            // record_status_chk();
            mcu_status_sync();
            mcu_car_info_sync();
            mcu_config();
            printLostContinue();
            sys_pwr_monitor();

            if (mIcReadRetry) {
                request_iccard();
            }

            if (mMcuInited && mPwrSet.sys_pwr) {
                autoReadIccard();
            }
		}
		break;
	case MAJOR_UART: // 串口
		{
			switch (minor)
			{
			case EXT_UART_DB9:
				{
                    logd("db9 recv len %d\n", size1);
					gbt19056.on_recv(data1, size1);
                    broadcast_msg(MCU_MSG_TYPE_DB9, data1, size1);
				}
				break;
			case EXT_UART_RS485:
				{
                    logd("rs485 recv len %d\n", size1);
					if (mRS485ExternalGps) {
						gps_handler(data1, size1);
					} else {
                    	broadcast_msg(MCU_MSG_TYPE_RS485, data1, size1);
					}
				}
				break;
            default:
                break;
			}
		}
		break;
	case MAJOR_IC_CARD:
        {
            handleIccardAck(minor, data1, size1);
        }
		break;
	case MAJOR_PRINTER:
        {
            logd("printer state %d", minor);
            switch (minor) {
                case PRINTER_MINOR_PRINT:
                    {
                        clock_gettime(CLOCK_MONOTONIC, &mPrinterTime);//打印时间
                        string dot = mPrint.get_oneline();
                        logd("printer data len %d\n", dot.size());

                        if (dot.size() == 0) { /*打印结束*/
                            IOMsg msg(MAJOR_PRINTER, PRINTER_MINOR_ROLL); /*留白*/
                            send(msg);
                        } else { /*持续打印， 丢包重发*/
                            IOMsg msg(MAJOR_PRINTER, PRINTER_MINOR_PRINT);
                            msg.data.append((const char *)dot.c_str(), dot.size());
                            send(msg);
                        }
                    }
                    break;
                case PRINTER_MINOR_ROLL:
                case PRINTER_MINOR_ALONE:
                    mPrint.clearBusy();
                    break;
                case PRINTER_MINOR_PRINT_REQ:
                    break;
                default:
                    break;
            }
        }
        break;
	case MAJOR_CAN:
        {
            if (minor == CAN_MINOR_SPEED_CAN0_MSG) {
                if (size1 != sizeof(UrtpCanT)) {
                    loge("Can Msg len error!\n");
                    break;
                }
                const UrtpCanT *p = reinterpret_cast<const UrtpCanT *>(data1);
                if(mCan.mVerbose > 0) {
                    logd("can[%d] ID[0x%x] len[%d]: %02x-%02x-%02x-%02x-%02x-%02x-%02x-%02x\n",\
                            minor, p->id, p->len,\
                            p->data[0],p->data[1],p->data[2],p->data[3],\
                            p->data[4],p->data[5],p->data[6],p->data[7]);
                }
                vehicle_io.can_signal_loss = 0;
                clock_gettime(CLOCK_MONOTONIC, &mCanMsgUpdateTime);
                decodeCanMsg(p);
                broadcast_msg(MCU_MSG_TYPE_RAW_CAN0, data1, size1);
            } else if (minor == CAN_MINOR_DISP_CAN1_MSG) {
                broadcast_msg(MCU_MSG_TYPE_RAW_CAN1, data1, size1);
            } else if (minor == CAN_MINOR_STATISTICS) {
                struct Can_stat {
                    uint32_t    can1_tx_msg;
                    uint32_t    can1_tx_act;
                    uint32_t    can1_rx_act;
                    uint32_t    can1_rx_msg;
                    uint32_t    can1_err;
                    uint32_t    can2_tx_msg;
                    uint32_t    can2_tx_act;
                    uint32_t    can2_rx_act;
                    uint32_t    can2_rx_msg;
                    uint32_t    can2_err;
                } *canStat = (struct Can_stat *)data1;
                logd("can1: tx-msg:%d tx-act:%d rx-act:%d rx-msg:%d err:%d can2: tx-msg:%d tx-act:%d rx-act:%d rx-msg:%d err:%d\n",\
                        canStat->can1_tx_msg, canStat->can1_tx_act, canStat->can1_rx_act, canStat->can1_rx_msg, canStat->can1_err,
                        canStat->can2_tx_msg, canStat->can2_tx_act, canStat->can2_rx_act, canStat->can2_rx_msg, canStat->can2_err);
            }
        }
		break;
	case MAJOR_GPS: {
#ifdef TESTGPSNMEA
                if(calGpsMiles.readonec){
                   calGpsMiles.GetLineNmea();
                   gps_handler(calGpsMiles.line, strlen(calGpsMiles.line));
                }
#else

        char value[PROP_VALUE_MAX];
        int len = __system_property_get(PROP_RW_MINIEYE_DISABLE_GPS, value);
        if (len > 0) {
            if (!strcmp(value, "true")) {
                logd("skip, see %s = %s", PROP_RW_MINIEYE_DISABLE_GPS, value);
                break;
            }
        }

        if (mNMEADataEnable & 1) {
            if (mLibflowServer) {
                mLibflowServer->send(data1, size1);
            }
        }

        if (mNMEADataEnable & 2) {
            if (mRbGNSS) {
                static uint32_t gnssFrameIdx = 1;
                RBFrame *pRbFrm = (RBFrame *) mRbGNSS->RequestWriteFrame(size1 + sizeof(RBFrame), CRB_FRAME_I_SLICE);
                if (CRB_VALID_ADDRESS(pRbFrm)) {
                    memset(pRbFrm, 0, sizeof(RBFrame));
                    pRbFrm->frameTag = RBFrameTag;
                    pRbFrm->frameType = IFrame;
                    pRbFrm->channel = 0;
                    pRbFrm->frameIdx = gnssFrameIdx++;
                    pRbFrm->frameNo  = pRbFrm->frameIdx;
                    pRbFrm->dataLen = size1;
                    pRbFrm->video.VWidth = 0;
                    pRbFrm->video.VHeight = 0;
                    struct timeval tv;
                    gettimeofday(&tv, nullptr);
                    pRbFrm->time = tv.tv_sec * 1000 + tv.tv_usec / 1000;
                    pRbFrm->pts = pRbFrm->time;
                    memcpy(pRbFrm->data, (char *) data1, size1);
                    mRbGNSS->CommitWrite();
                }
            }
        }
        if (!mRS485ExternalGps) {
            gps_handler(data1, size1);
        }
#endif
	}
        break;
	case MAJOR_VERSION:
        {
            if (minor == VERSION_BASE) {
                mMcuVersionOk = true;
                logd("Mcu version(%d): %s", size1, data1);
                mMcuVersion.assign(data1);
                sendCommand(MCU_MSG_TYPE_MCU_VERSION, (const uint8_t *)data1, size1);
            } else if (minor == VERSION_FIRMWARE_INFO) { /* firmware info */
                logd("recv Mcu firmware info\n");
                sendCommand(MCU_MSG_TYPE_MCU_FIRMWARE_INFO, (const uint8_t *)data1, size1);
            } else if (minor == VERSION_AUTHEN) { /* authen ack */
                logd("recv Mcu authen ack\n");
                sendCommand(MCU_MSG_TYPE_MCU_UPGRD_AUTHEN, (const uint8_t *)data1, size1);
            }
        }
        break;
    case MAJOR_RESET:
        {
            logd("mcu reset ack\n");
        }
        break;
    case MAJOR_BOOT_MODE:
        {
            if (minor == CMD_MCU_BLD_MODE) {
                logd("mcu boot bld mode\n");
                mLogger->mlog("mcu boot bld mode\n");
                sendCommand(MCU_MSG_TYPE_MCU_BLD_MODE, NULL, 0);
            } else if (minor == CMD_MCU_APP_MODE) {
                logd("mcu boot app mode\n");
                mLogger->mlog("mcu boot app mode\n");
                sendCommand(MCU_MSG_TYPE_MCU_APP_MODE, NULL, 0);
            } else if (minor == CMD_MCU_ALONE_MODE) {
                logd("mcu boot alone mode\n");
                mLogger->mlog("mcu boot alone mode\n");
            }
        }
        break;
    case MAJOR_UPGRADE:
        {
            logd("recv mcu upgrade ack\n");
            sendCommand(MCU_MSG_TYPE_UPGRADE, (const uint8_t *)data1, size1);
        }
        break;
    case MAJOR_FT_TEST:
        {
            if (minor == FT_SOC_RS232_1) {
                IOMsg msg(MAJOR_FT_TEST, FT_SOC_RS232_1);
                int32_t ret = run_ft_test("mcu_tool -Trs232_1");
                if (!ret) {
                    msg.data.append("SOC RS232_1 TEST OK");
                } else {
                    msg.data.append("SOC RS232_1 TEST FAIL");
                }
                send(msg);
            } else if(minor == FT_SOC_RS232_2) {
                IOMsg msg(MAJOR_FT_TEST, FT_SOC_RS232_2);
                int32_t ret = run_ft_test("mcu_tool -Trs232_2");
                if (!ret) {
                    msg.data.append("SOC RS232_2 TEST OK");
                } else {
                    msg.data.append("SOC RS232_2 TEST FAIL");
                }
                send(msg);
            }
        }
        break;
    case MAJOR_GET_REG:
        {
            struct REG
            {
                uint32_t addr;
                uint32_t val;
            };
            struct REG *reg = (REG *)data1;
            logd("the mcu reg(0x%08X) value is 0x%08X\n", reg->addr, reg->val);
        }
        break;
	default:
		{
			logw("[IOMsgHandler::proc] unkown message: major=[0x%02x], minor=[0x%02x], data=[%d].", major, minor, size1);
		}
	}
}

uint64_t system_time(void) {
    uint64_t ns = 0;

    struct timespec now;
    clock_gettime(CLOCK_MONOTONIC, &now);
    ns = now.tv_sec*1000000000UL + now.tv_nsec;

    return ns;
}

void IOMsgHandler::handleAspeed(float freq)
{
    uint64_t now;
    static int contigousBigAccl = 0;
    static uint64_t lastPulseTime;
    bool bValid = true;
    double speed = 0.0;
    static double last_speed = 0.0;
    double speedAccl = 0.0;

    double ratio = mCan.mConfig->mConfig.analog_cfg.aspeed.ratio;

    now = system_time();

    mLastFreq = freq;

#if 0
    if (!mCan.mConfig->mConfig.analog_cfg.aspeed.enable) {
        return;
    }
#endif

    //For factory test scenario
    if(mMode == WORK_MODE_FT) {
        mGpsCalibEnable = false;
        ratio = 1.0;
    }

    if (mCan.mConfig->mConfig.analog_cfg.aspeed.disableGpsCal) {
        mGpsCalibEnable = false;
    }

    if (mGpsCalibEnable) {
        if (mGpsCalibRatio >= 1e-6) {
            ratio = mGpsCalibRatio;
        }
    }
    speed = ratio * freq;
    //logd("gps %.1f aspeed %.1d fre %.2f ratio %f", mLbs.speed_x10/10.0, speed, freq, ratio);

    speedAccl = (speed - last_speed);
    uint32_t ms_add = (now-lastPulseTime)/1000000;


    if (mLogVerbose[LOG_TYPE_ASPEED] > 0) {
        logd("gps %.1f aspeed %.1f last %.1f ratio %lf fre %.1f ms %u spd_abs %.1lf\n", mLbs.speed_x10/10.0, speed, last_speed, ratio, freq, ms_add, fabs(speedAccl));
    }

    #define MAX_SPEED_ACCL  (20)
    if (::fabs(speedAccl) > MAX_SPEED_ACCL) {
        logw("Drop invalid speed %f last speed %f accl %f", speed, last_speed, speedAccl);
        mLogger->mlog("Drop invalid speed %f last speed %f accl %f", speed, last_speed, speedAccl);
        contigousBigAccl ++;
        bValid = false;
    }
    #define  CONTIGOUS_MAX (15)
    if (contigousBigAccl >= CONTIGOUS_MAX) {
        logw("contigous flowout speed %.1f\n", speed);
        mLogger->mlog("contigous flowout speed %.1f\n", speed);
        bValid = true;
    }
    if (bValid) {
        last_speed = speed;
        mPulses_speed_x10 = speed*10;
        lastPulseTime = now;
        contigousBigAccl = 0;
    }
}

void IOMsgHandler::calib_aspeed(void)
{
    double calibRatio = 0.0f, speedX10 = (double)mLbs.speed_x10;
    CalibMidState midstate;
    float freq = mLastFreq;

    int32_t ret = mGpsSpdCalib->Update(speedX10 / 10, freq, &calibRatio, &midstate);

    if (mLogVerbose[LOG_TYPE_ASPEED] > 0) {
        logd("Ret %02d | %6.3f %6.3f | size %05d sum %8.3f avg %8.3f var %8.3f stddev %8.3f result %8.3f",
                ret,
                mInfo.speed, freq,
                midstate.sampleNum,
                midstate.sum,
                midstate.avg,
                midstate.variance,
                midstate.stddev, calibRatio);
    }

    if (0 == ret) {
        mLogger->mlog("calib done Ratio %f", calibRatio);
        mGpsCalibRatio = calibRatio;

        char value[PROP_VALUE_MAX];
        memset(value, 0, sizeof(value));
        snprintf(value, sizeof(value), "%f", calibRatio);
        __system_property_set(PROP_RW_MINIEYE_GPS_CALIB_RATIO, value);

        memset(value, 0, sizeof(value));
        snprintf(value, sizeof(value), "%f", midstate.stddev);
        __system_property_set(PROP_RW_MINIEYE_CALIB_STDDEV, value);
    }
}

void IOMsgHandler::set_speed(void)
{
    static int last_speed_x10 = 0;
    int fakespeed = -1;
    get_debug_speed(fakespeed); /* 如果假速度存在就使用假速度 */

    struct timespec now;
    static struct timespec last = {0, 0};
    clock_gettime(CLOCK_MONOTONIC, &now);

    if (fakespeed >= 0) {
        if (mSpeedLimit > 0) {
            if (fakespeed > mSpeedLimit) {
                fakespeed = mSpeedLimit;
            }
        }
        mSpeed_x10 = fakespeed;
    } else {
        SPD_SRC_E spdSrc = SPD_SRC_MAX;
        int speed = 0;

        /* GPS 速度 */
        if (mCan.mConfig->mConfig.useGpsSpeed) {
            //logd("--update gps speed\n");

            if (mSpeedLimit > 0) {
                if (mLbs.speed_x10 > mSpeedLimit) {
                    speed = mSpeedLimit;
                } else {
                    speed  = mLbs.speed_x10;
                }
            } else {
                speed  = mLbs.speed_x10;
            }
            
            spdSrc = SPD_SRC_GPS;
        }

        /* 设置模拟速度 */
        if (mCan.mConfig->mConfig.analog_cfg.aspeed.enable) {
            if (mSpeedLimit > 0) {
            
                if (mPulses_speed_x10 > mSpeedLimit) {
                    speed = mSpeedLimit;
                } else {
                    speed = mPulses_speed_x10;
                }
                
            } else {
                speed  = mPulses_speed_x10;
            }
        
            spdSrc = SPD_SRC_ANALOG;
            //logd("aspeed %d\n", mSpeed_x10/10);
        }

        if (SPD_SRC_MAX == spdSrc) {
            spdSrc = SPD_SRC_CAN;
            if (mCanSpeedUpdateTime.elapsed() < 2000) {
                speed = mCan.mCarInfo.speed * 10;
            } else {
                speed = 0;
            }

            if (mSpeedLimit > 0) {
                if (speed > mSpeedLimit) {
                    speed = mSpeedLimit;
                }
            }
            
            mCan_speed_x10 = speed;
            //logd("--update can speed\n");
            if (WORK_MODE_FT == mMode) {
                speed = 0;
            }
        }

        mSpeed_x10 = speed;
        mSpdSrc    = spdSrc;
    }

    if (mAccOffEvent) {
        logd("acc off, clear speed");
        mSpeed_x10 = 0;
    }

    /* can 转向没有判断使能， 放在前面 */
    if (mCanTurnUpdateTime.elapsed() < 2000) {
        mCan_turnl = mCan.mCarInfo.turn & 0x01;
        mCan_turnr = (mCan.mCarInfo.turn >> 1) & 0x01;

        if (mMode != WORK_MODE_FT) {
            mTurnl = mCan_turnl;
            mTurnr = mCan_turnr;
        }
        //logd("--update can turn\n");
    }
    /* 设置模拟转向 */
    if (mCan.mConfig->mConfig.analog_cfg.analogSignals[AS_IO_TURNLAMP].enable) {
        mTurnl = mPulses_turnl;
        mTurnr = mPulses_turnr;
    }

    /* 如果使用的can速度或者转向 */
    if (!mCan.mConfig->mConfig.analog_cfg.analogSignals[AS_IO_TURNLAMP].enable || \
            (!mCan.mConfig->mConfig.useGpsSpeed && !mCan.mConfig->mConfig.analog_cfg.aspeed.enable )) {

        if (now.tv_sec*1000 + now.tv_nsec/1000000 >= mCanMsgUpdateTime.tv_sec*1000 + mCanMsgUpdateTime.tv_nsec/1000000 + 4000) { /* can消息超时 */
            vehicle_io.can_signal_loss = 1;
        }
    }

    if (mAdisp_mode == ADISP_SPEED) { /*adas display speed mode */
        if (mSpeed_x10 != last_speed_x10) {
            mCan.sendSpeedToDisp(mSpeed_x10/10);
        }
    }

    if (now.tv_sec < last.tv_sec + 1) { /* 限制写日志速度最少间隔1秒*/
        return;
    }

    if (mLogVerbose[LOG_TYPE_STATUS] > 0) {
        if (mSpeed_x10 != last_speed_x10 || now.tv_sec >= last.tv_sec + 5) {
            last = now;
            mLogger->mlog("mcu(%s) soc(%s) acc %d sys_pwr %d 12v %d 5v %d bat_char %d bat_dischar %d "
                "fmcu_rst_soc %d fauto_ctrl_cap %d fauto_ctrl_bat %d pwr_off %d pwr_low %d DIN %.1fv "
                "speed_x10 %u turnl %d turnr %d locate %d lon %f lat %f sat %d antenna %d tempture %.1f",
                    uptime(mMcuUptime).c_str(),\
                    uptime(now.tv_sec).c_str(),\
                    vehicle_io.acc,\
                    mPwrSet.sys_pwr,\
                    mPwrSet.pwr_ext_12v,\
                    mPwrSet.pwr_ext_5v,\
                    mPwrSet.bat_charging,\
                    mPwrSet.bat_discharging,\
                    mPwrSet.fmcu_rst_soc, \
                    mPwrSet.fauto_ctrl_cap, \
                    mPwrSet.fauto_ctrl_bat, \
                    mSys.power_off,\
                    mSys.power_low,\
                    mAdcVoltage[ADC_CHN_24V],\
                    mSpeed_x10,\
                    mTurnl,\
                    mTurnr,\
                    mLbs.status,\
                    mLbs.lng_x1kw / 10000000.0,\
                    mLbs.lat_x1kw / 10000000.0,\
                    mLbs.sat,\
                    mLbs.antenna,\
                    mMcuTempture_x100/100.0);
        }
    }
    last_speed_x10 = mSpeed_x10;
}

/*
    canId:can报文的id
    sig:can信号参数
    return:true是can信号的报文；false不是can信号的报文
*/
bool IOMsgHandler::isCanSig(uint32_t canId, const can_signal &sig)
{
    if ((canId == sig.id) 
        || ((sig.mask != 0) && ((canId & sig.mask) == (sig.id & sig.mask)))) {
        /* 
            列表模式下报文id与can id一致则判定该报文是can信号的报文。
            掩码过滤模式下只关注屏蔽码mask中值为1的bit位，我们称之为确定位；验证码id中确定位的值为确定值；
            报文canId的确定位的值与验证码id的确定值一致则判定该报文为can信号的报文。
            例如：mask的bit0~bit3为1即mask=0xf;id=0x3;
                  报文id的bit0~bit3的值为0x3的所有报文都是can信号的报文;如0x13\0x23都是符合要求的can报文。
        */
        return true;
    } else {
        return false;
    }
}

uint8_t IOMsgHandler::decodeCanMsg(const UrtpCanT *pCan)
{
    int ret = 0;

    for (auto & r : mCan.mConfig->mConfig.csMap) {
        if (isCanSig(pCan->id, r.second)) {
            double value = 0;
            can_decode_signal(&r.second, pCan->data, pCan->len, &value);
            switch (r.first) {
                case CS_SPEED: {
                    mCan.mCarInfo.speed = value;
                    mCanSpeedUpdateTime = my::timestamp::now();
                    break;
                }
                case CS_LEFT:
                case CS_RIGHT:
                {
                    static std::map<CS_TYPE_E, my::timestamp> last_on;
                    mCanTurnUpdateTime = my::timestamp::now();
                    can_decode_signal(&r.second, pCan->data, pCan->len, &value);
                    uint8_t turn = 1 + (r.first == CS_RIGHT);
                    if (value) {
                        mCan.mCarInfo.turn |= turn;
                        last_on[r.first] = mCanTurnUpdateTime;
                    } else {
                        auto it = last_on.find(r.first);
                        /* turn off > 600 ms */
                        if ((it == last_on.end()) ||
                            (it->second.elapsed() > 600)) {
                            mCan.mCarInfo.turn &= ~(turn);
                        }
                    }
                    break;
                }
                case CS_DOOR_FRONT:
                case CS_DOOR_MID:
                {
                    static std::map<CS_TYPE_E, my::timestamp> last_open;
                    mCanDoorOpenStatTS = my::timestamp::now();
                    can_decode_signal(&r.second, pCan->data, pCan->len, &value);
                    uint8_t door = 1 + (r.first == CS_DOOR_MID);
                    if (value) {
                        mCan.mCarInfo.doorBits |= door;
                        last_open[r.first] = mCanDoorOpenStatTS;
                    } else {
                        auto it = last_open.find(r.first);
                        /* turn off > 600 ms */
                        if ((it == last_open.end()) ||
                            (it->second.elapsed() > 600)) {
                            mCan.mCarInfo.doorBits &= ~(door);
                        }
                    }
                    break;
                }
                case CS_REVERSE: {
                    mCan.mCarInfo.reverse = !!value;
                    break;
                }
                case CS_BRAKE: {
                    mCan.mCarInfo.brake = !!value;
                    break;
                }
                case CS_ACCELERATOR: {
                    mCan.mCarInfo.accelerator = (uint8_t)value;
                    break;
                }
                case CS_GEAR: {
                    mCan.mCarInfo.gear = (uint8_t)value;
                    break;
                }
                case CS_EHB_STATE: {
                    mCan.mCarInfo.EHB_state = !!value;
                    break;
                }
                case CS_EHB_PARK_REQ: {
                    mCan.mCarInfo.EHB_park_req = !!value;
                    break;
                }
                case CS_EHB_PARK_DONE: {
                    mCan.mCarInfo.EHB_park_done = !!value;
                    break;
                }
                case CS_FRONT_LEFT_WHEEL_SPD: {
                    mCan.mCarInfo.FLWheelSpd = value;
                    break;
                }
                case CS_FRONT_LEFT_WHEEL_STAT: {
                    mCan.mCarInfo.FLWheelSpdStat = (uint8_t)value;
                    break;
                }
                case CS_FRONT_RIGHT_WHEEL_SPD: {
                    mCan.mCarInfo.FRWheelSpd = value;
                    break;
                }
                case CS_FRONT_RIGHT_WHEEL_STAT: {
                    mCan.mCarInfo.FRWheelSpdStat = (uint8_t)value;
                    break;
                }
                case CS_BACK_LEFT_WHEEL_SPD: {
                    mCan.mCarInfo.BLWheelSpd = value;
                    break;
                }
                case CS_BACK_LEFT_WHEEL_STAT: {
                    mCan.mCarInfo.BLWheelSpdStat = (uint8_t)value;
                    break;
                }
                case CS_BACK_RIGHT_WHEEL_SPD: {
                    mCan.mCarInfo.BRWheelSpd = value;
                    break;
                }
                case CS_BACK_RIGHT_WHEEL_STAT: {
                    mCan.mCarInfo.BRWheelSpdStat = (uint8_t)value;
                    break;
                }
                case CS_MILE_TOTAL: {
                    mCan.mCarInfo.mileTotal = value;
                    break;
                }
                case CS_MILE_TRIP: {
                    mCan.mCarInfo.mileSingle = value;
                    break;
                }
                case CS_FUEL_TOTAL: {
                    mCan.mCarInfo.fuelTotal = value;
                    break;
                }
                case CS_FUEL_AVG: {
                    mCan.mCarInfo.fuelAvg = value;
                    break;
                }
                case CS_FUEL_LEFT: {
                    mCan.mCarInfo.fuelLeft = value;
                    break;
                }
                case CS_TURN_SPEED: {
                    mCan.mCarInfo.turnSpeed = value;
                    break;
                }
                default: {
                    break;
                }
            }
        }
    }

    if(mCan.mVerbose > 0 || mLogVerbose[LOG_TYPE_CAN]) {
        logd("CAN: turn %d speed %.2f brake %d reverse %d mile_single %10f mile_total %10f fuel_total %10f fuel_avg %10f fuel_left %10f turn_speed %10f\n",
            mCan.mCarInfo.turn, mCan.mCarInfo.speed, mCan.mCarInfo.brake, mCan.mCarInfo.reverse, mCan.mCarInfo.mileSingle, mCan.mCarInfo.mileTotal, mCan.mCarInfo.fuelTotal, mCan.mCarInfo.fuelAvg, mCan.mCarInfo.fuelLeft, mCan.mCarInfo.turnSpeed);
    }
    return ret;
}

void IOMsgHandler::setLbsAntenna(const nmeaINFO &mInfo, bool defaultAntenna){
    if (defaultAntenna) {
        mLbs.antenna = ANTENNA_OK;
    } else {
        if (mInfo.txt != NULL) {
            //logd("External GPS txt info %.20s ", mInfo.txt);
            if(strstr(mInfo.txt, "OPEN")) // 天线状态
                mLbs.antenna = ANTENNA_OPEN;
            else if(strstr(mInfo.txt, "OK"))
                mLbs.antenna = ANTENNA_OK;
            else if(strstr(mInfo.txt, "SHORT"))
                mLbs.antenna = ANTENNA_SHORT;
        }
    }
}

void IOMsgHandler::setLbsStatus(const nmeaINFO &mInfo, bool defaultStatus, bool defaultSig){

    if (defaultStatus) {
        mLbs.status = 1;
    } else {
        mLbs.status = mInfo.fix != 1;             // 1未定位, 2 2D定位 3 3D定位
    }

    if (defaultSig) {
        mLbs.sat = 8;
        mLbs.sig_level = 1;
    } else {
        gps_sig_handler();
    }

    if (mInfo.PDOP >= 6.0 || mInfo.speed >= 119) { /* 车速限制最快是120km，信号质量太差 */
        logd("Invalid GPS info; PDOP (%f), speed (%f)", mInfo.PDOP, mInfo.speed);
        // CalGpsMiles & calGpsMiles = CalGpsMiles::getInstance();
        // calGpsMiles.nmeaErrorCount++;
        return;
    }

    if (mEnableGpsTime) {
        gps_get_time(mInfo);
    }

    clock_gettime(CLOCK_MONOTONIC, &mGpsUpdateTime);
    mDeg_lat = nmea_ndeg2degree(mInfo.lat);
    mDeg_lon = nmea_ndeg2degree(mInfo.lon);

    /*更新GPS经纬度, 不定位时，保留上次经纬度*/
    if(mLbs.status) {
        mLbs.lat_x1kw = mDeg_lat * 10000000;
        mLbs.lng_x1kw = mDeg_lon * 10000000;

        if (mInfo.elv < 6000) {
            mLbs.alt_x10 = mInfo.elv * 10; // 高度
        } else {
            mLogger->mlog("alt %f is bigger than 6000m, not using", mInfo.elv);
        }
        
        update_last_gps_location();
    }
    mLbs.dir_x100 = mInfo.direction * 100; // 方向
    mLbs.speed_x10 = mInfo.speed * 10; // 速度 km/h
}

void IOMsgHandler::gps_handler(const char *data1, int size1)
{
    // CalGpsMiles & calGpsMiles = CalGpsMiles::getInstance();
    if (mGpsNmeaLog) {
        mNmeaLogger->mlog_raw(data1, size1);
    }

    if (mGpsNmeaToDB9) {
        // 测试：发送nmea信息到DB9
        IOMsg msg(MAJOR_UART, EXT_UART_DB9);
        msg.data.append(data1, size1);
        send(msg);
        logd("send nmea log to db9");
    }

    if (mLogVerbose[LOG_TYPE_GPS] >= 3) {
        char buf[2048];
        char *p = &buf[0];
        for(int i=0; i<size1; i++) {
            if(data1[i] == 0 || data1[i] == '\r' || data1[i] == '\n') {
                p[i] = ' ';
            }else {
                p[i] = data1[i];
            }
        }
        p[size1] = 0;
        logd("gps_raw(%d): %s\n", size1, p);
    }
    if(mGpsMsgPass) {
        broadcast_msg(MCU_MSG_TYPE_GPS_CMD, data1, size1);
    }

    int nread = nmea_parse(&mParser, (const char *)data1, size1, &mInfo);
    if(nread <= 0) {
        logd("gps_handler nmea_parse error return\n");
        // calGpsMiles.nmeaErrorCount++;
        return;
    }

    if (mInfo.txt != NULL) {
        //logd("External GPS txt info %.20s ", mInfo.txt);
        if(strstr(mInfo.txt, "OPEN")) // 天线状态
            mLbs.antenna = ANTENNA_OPEN;
        else if(strstr(mInfo.txt, "OK"))
            mLbs.antenna = ANTENNA_OK;
        else if(strstr(mInfo.txt, "SHORT"))
            mLbs.antenna = ANTENNA_SHORT;
    }

    // 外置高精度地图模块没有天线状态
    if (mRS485ExternalGps) {
        mLbs.antenna = ANTENNA_OK;
    }

    mLbs.status = mInfo.fix != 1;             // 1未定位, 2 2D定位 3 3D定位

    gps_sig_handler();
    if (mEnableGpsTime) {
        logd("gps time");
        gps_get_time();
    } else {
        logd("no gps time");
    }
    //gps_filter();
    gps_log_record();

    /* 车速限制最快是120km，信号强度大于0的卫星数量最少为5颗，GPS信号质量太差 */
    if (mInfo.PDOP >= mPDOPThres || mInfo.speed >= 119 || mLbs.sat < 5) {
        logd("Invalid GPS info; PDOP (%f), speed (%f), sat:%d", mInfo.PDOP, mInfo.speed, mLbs.sat);

        if (mLocTs.elapsed() >= 5000) {/*不定位超时清零速度*/
            mLbs.speed_x10 = 0;
        }
        return;
    }
    clock_gettime(CLOCK_MONOTONIC, &mGpsUpdateTime);

    mDeg_lat = nmea_ndeg2degree(mInfo.lat);
    mDeg_lon = nmea_ndeg2degree(mInfo.lon);

    /*更新GPS经纬度, 不定位并且acc on时，保留上次经纬度*/
    if(mLbs.status && !mAccOffEvent) {
        mLocTs = my::timestamp::now();
        mLbs.lat_x1kw = mDeg_lat * 10000000;
        mLbs.lng_x1kw = mDeg_lon * 10000000;

        if (mInfo.elv < 6000 || mInfo.elv > -200) {
            mLbs.alt_x10 = mInfo.elv * 10; // 高度
        }
        
        update_last_gps_location();
    } else {
        logd("mLbs.status : %d, mAccOffEvent : %d, not update gps", mLbs.status, mAccOffEvent);
    }

    if ((mSpdSatNumThres <= 0) && (mSpdSatNumThresTs.elapsed() > 5000)) {
        mSpdSatNumThresTs = my::timestamp::now();
        char propValue[PROP_VALUE_MAX];
        memset(propValue, 0, sizeof(propValue));
        if (__system_property_get(PROP_RW_MINIEYE_GPS_SPD_SAT_NO_THRES, propValue) > 0) {
            mSpdSatNumThres = atoi(propValue);
        }
    }
    mLbs.dir_x100 = mInfo.direction * 100; // 方向
    if (mLocTs.elapsed() >= mGpsSpdTimeout) {/*不定位超时清零速度*/
        mLbs.speed_x10 = 0;
    } else if (mSpdSatNumThres > 0) { /*规避信号问题导致速度过大，卫星个数根据项目实测要求*/
        my::uint64 curMs = (my::uint64)my::timestamp::now();
        if (mLbs.sat < mSpdSatNumThres) {
            int avgSpd = 0;
            for (auto r : mGpsSpdX10Buf) {
                avgSpd += r.second;
            }
            conf_t& sys = Manager::getInstance().sys;
            int curSpd = mInfo.speed;
            if (mInfo.speed > sys.warn.overspeed.limit) {
                avgSpd += sys.warn.overspeed.limit * 10;
            } else {
                avgSpd += curSpd * 10;
            }
            avgSpd /= mGpsSpdX10Buf.size() + 1;
            mLbs.speed_x10 = avgSpd;
            mGpsSpdX10Buf.insert(std::pair<my::uint64, int>(curMs, curSpd * 10));
        } else {
            mLbs.speed_x10 = mInfo.speed * 10; // 速度 km/h
            mGpsSpdX10Buf.insert(std::pair<my::uint64, int>(curMs, mLbs.speed_x10));
        }

        auto it = mGpsSpdX10Buf.begin();
        while (it != mGpsSpdX10Buf.end()) {
            if ((curMs - it->first) >= 30000) {
                it = mGpsSpdX10Buf.erase(it);
            } else {
                break;
            }
        }
    } else {
        mLbs.speed_x10 = mInfo.speed * 10; // 速度 km/h
    }

    calib_aspeed();
}

void IOMsgHandler::gps_get_time(void)
{
    struct tm stm;
    stm.tm_year        = mInfo.utc.year;
    stm.tm_mon         = mInfo.utc.mon - 1;
    stm.tm_mday        = mInfo.utc.day;
    stm.tm_hour        = mInfo.utc.hour;
    stm.tm_min         = mInfo.utc.min;
    stm.tm_sec         = mInfo.utc.sec;
    stm.tm_isdst       = -1;

    time_t         now = time(NULL);
    struct tm      tm_local;
    struct tm      tm_utc;
    unsigned long  time_local, time_utc;
    gmtime_r(&now, &tm_utc);
    localtime_r(&now, &tm_local);
    tm_local.tm_isdst = -1;
    tm_utc.tm_isdst = -1;
    time_local = mktime(&tm_local);
    time_utc = mktime(&tm_utc);
    long utc_diff = time_utc - time_local;
    mLbs.time = (long long)(mktime(&stm) - utc_diff);

    // 如果GPS定位，判断是否需要校时, 时间大于2011年1月1日才认为有效
    if ((mLbs.status) && (mLbs.time > 1293811200)) {
        auto use_gps_time = [](unsigned int gps_time, unsigned int diff) {
            unsigned int now = my::timestamp::utc_seconds();
            return (abs((int64_t)gps_time - (int64_t)now) > diff);
        };
        // GPS时间和系统时间相差10s就会触发校时,
        int timeDiffThres = 10;
        if (mLastPropGetTs.elapsed() > (10 * 1000)) {
            mLastPropGetTs = my::timestamp::now();
            char propValue[PROP_VALUE_MAX] = {0};
            memset(propValue, 0, sizeof(propValue));
            if (__system_property_get(PROP_RW_MINIEYE_GPS_TIME_SYNC_THRES, propValue) > 0) {
                int val = atoi(propValue);
                if (val > 0) {
                    timeDiffThres = val;
                }
            }
        }

        if (use_gps_time(mLbs.time, timeDiffThres)) {
            // 30s防抖，规避短时间GPS时间异常的情况
            if (mLastGpsTimeTs.elapsed() > (30 * 1000)) {
                mGpsLogger->mlog("gps_time %u sys_time %u, set rtc !!!", mLbs.time, my::timestamp::utc_seconds());
                struct timeval stime = {0, 0};
                stime.tv_sec = mLbs.time;
                settimeofday(&stime, NULL);
                // 设置硬件时钟
                system("qcom_date -R &");
            }
        } else {
            mLastGpsTimeTs = my::timestamp::now();
        }
    } else {
        mLastGpsTimeTs = my::timestamp::now();
    }

}

void IOMsgHandler::gps_get_time(const nmeaINFO info)
{
    struct tm stm;
    stm.tm_year        = info.utc.year;
    stm.tm_mon         = info.utc.mon - 1;
    stm.tm_mday        = info.utc.day;
    stm.tm_hour        = info.utc.hour;
    stm.tm_min         = info.utc.min;
    stm.tm_sec         = info.utc.sec;
    stm.tm_isdst       = -1;

    time_t         now = time(NULL);
    struct tm      tm_local;
    struct tm      tm_utc;
    unsigned long  time_local, time_utc;
    gmtime_r(&now, &tm_utc);
    localtime_r(&now, &tm_local);
    tm_local.tm_isdst = -1;
    tm_utc.tm_isdst = -1;
    time_local = mktime(&tm_local);
    time_utc = mktime(&tm_utc);
    long utc_diff = time_utc - time_local;
    mLbs.time = (long long)(mktime(&stm) - utc_diff);

    // 如果GPS定位，判断是否需要校时, 时间大于2011年1月1日才认为有效
    if (mLbs.status &&  mLbs.time > 1293811200) {
        auto use_gps_time = [](unsigned int gps_time, unsigned int diff) {
            unsigned int now = my::timestamp::utc_seconds();
            return (abs((int64_t)gps_time - (int64_t)now) > diff);
        };
        // GPS时间和系统时间相差10s就会触发校时
        if (use_gps_time(mLbs.time, 10)) {
            mGpsLogger->mlog("gps_time %u sys_time %u, set rtc !!!", mLbs.time, my::timestamp::utc_seconds());
            struct timeval stime = {0, 0};
            stime.tv_sec = mLbs.time;
            settimeofday(&stime, NULL);
            // 设置硬件时钟
            system("qcom_date -R &");
        }
    }
}



double lat = 0.0;
double lon = 0.0;
double gspeed = 0.0;
int located = 0;
bool playback_open = false;
bool playback_over = false;
#define GPS_PLAYBACK_FILE "/data/minieye/gps_playback.txt"
void gps_playback(void)
{
    static FILE *fp = NULL;
    char line[4096];
    char *p = line;

    if(playback_over) {
        logd("read log file over");
        return;
    }

    if(!playback_open) {
        fp = fopen(GPS_PLAYBACK_FILE, "r");
        if (!fp) {
            logd("open %s fail\n", GPS_PLAYBACK_FILE);
            return;
        }
    }
    playback_open = true;

    if (fgets(line, sizeof(line), fp)) {
        sscanf(line, "%lf,%lf,%d,%lf", &lon, &lat, &located, &gspeed);
        //logd("%lf,%lf,%d,%lf\n", lon, lat, located, gspeed);
    } else {
        logd("fgets fail\n");
        fclose(fp);
        playback_open = false;
        playback_over = true;
    }
}

void gps_fix_output(char *log)
{
    char name[128];
    snprintf(name, sizeof(name), "%s.fix", GPS_PLAYBACK_FILE);

    FILE *fpw = fopen(name, "a+");
    if(!fpw) {
        logd("open %s fail", name);
        return;
    }

    fprintf(fpw, "%s", log);
    fclose(fpw);
}

/*********
 * $GPGSV
 * $GPGSA
 * $GPTXT
 *
 * $BDGSA
 * $BDGSV
 *
 * $GNZDA
 * $GNGGA
 * $GNVTG
 * $GNRMC
 * $GNGLL
 * ***********/
void IOMsgHandler::gps_filter(void)
{
    static uint32_t skip_cnt = 0;
    static uint64_t recv_cnt = 0;
    double cur_lat = 0.0;
    double cur_lon = 0.0;

    cur_lat = nmea_ndeg2degree(mInfo.lat);
    cur_lon = nmea_ndeg2degree(mInfo.lon);

#if 0
    /* gps playback */
    if(mGpsPlayBack) {
        gps_playback();
        cur_lat = lat;
        cur_lon = lon;
        mInfo.speed = gspeed;
        mInfo.fix = located ? 3 : 0;
    }
#endif

    if (fabs(cur_lat - mDeg_lat) < 1e-7 && fabs(cur_lon - mDeg_lon) < 1e-7) {
        //logd("gps location the same: %.7lf %.7lf %.7lf %.7lf", cur_lat, cur_lon, mDeg_lat, mDeg_lon);
        return;
    }

#if 0
    double s = get_distance(cur_lat, cur_lon, mDeg_lat, mDeg_lon);
    logd("gps location %d distance %lf speed %lf cur_la %lf cur_lo %lf last_la %lf last_lo %lf", mInfo.fix, s, mInfo.speed, cur_lat, cur_lon, mDeg_lat, mDeg_lon);
    mDeg_lat = cur_lat;
    mDeg_lon = cur_lon;
    return;
#endif

    if (mInfo.fix == 2 || mInfo.fix == 3) { /* located */
        //logd("gps recv_cnt %d", recv_cnt);
        if (recv_cnt >= 5) { /* 收到5次定位后使能过滤 */
            double s = get_distance(cur_lat, cur_lon, mDeg_lat, mDeg_lon);
            //logd("gps location %d distance %lf speed %lf cur_la %lf cur_lo %lf last_la %lf last_lo %lf", mInfo.fix, s, mInfo.speed, cur_lat, cur_lon, mDeg_lat, mDeg_lon);
            if (s > 0.5) { /* 大于0.5km, dropped */
                skip_cnt++;
                
                logd("gps_skip_cnt %u distance %lf cur_la %lf cur_lo %lf last_la %lf last_lo %lf", skip_cnt, s, cur_lat, cur_lon, mDeg_lat, mDeg_lon);
                if (mLogVerbose[LOG_TYPE_GPS] > 0) {
                    mGpsLogger->mlog("gps_skip_cnt %u distance %lf cur_la %lf cur_lo %lf last_la %lf last_lo %lf", skip_cnt, s, cur_lat, cur_lon, mDeg_lat, mDeg_lon);
                }
            } else { /* */
                skip_cnt = 0;
            }

            if (skip_cnt >0 && skip_cnt <=5) { /* filter*/
                logd("gps location skip %d recv %d", skip_cnt, recv_cnt);
                return;
            } else if(skip_cnt > 5) {
                logd("gps location skipping used");
                if (mLogVerbose[LOG_TYPE_GPS] > 0) {
                    mGpsLogger->mlog("gps location skipping used");
                }
                skip_cnt = 0;
                recv_cnt = 0; /*误差坐标转正后，重新接受5帧后再使能过滤功能*/
            }
        }

        mDeg_lat = cur_lat;
        mDeg_lon = cur_lon;
        recv_cnt++;
#if 0
        if(playback_open) {
            char log[128];
            snprintf(log, sizeof(log), "%lf,%lf\n", mDeg_lon, mDeg_lat);
            gps_fix_output(log);
        }
#endif
    } else { /* 不定位之后清0*/
        skip_cnt = 0;
        recv_cnt = 0;
    }
}

void IOMsgHandler::gps_log_record(void)
{
    if (mLogVerbose[LOG_TYPE_GPS] >= 2) {
        logd("gps read: mask 0x%x sig %d fix %d PDOP %.1f HDOP %.1f VDOP %.1f lat %.4f lon %.4f elv %.1f "\
                "sog %.1f speed %.1f dir %.2f, dec %.2f mode %c sat %d use %d BDsat %d BDuse %d\n"\
                ,mInfo.smask
                ,mInfo.sig        /**< GPS quality indicator (0 = Invalid; 1 = Fix; 2 = Differential, 3 = Sensitive) */
                ,mInfo.fix        /**< Operating mode, used for navigation (1 = Fix not available; 2 = 2D; 3 = 3D) */

                ,mInfo.PDOP       /**< Position Dilution Of Precision */
                ,mInfo.HDOP       /**< Horizontal Dilution Of Precision */
                ,mInfo.VDOP       /**< Vertical Dilution Of Precision */

                ,mInfo.lat        /**< Latitude in NDEG - +/-[degree][min].[sec/60] */
                ,mInfo.lon        /**< Longitude in NDEG - +/-[degree][min].[sec/60] */
                ,mInfo.elv        /**< Antenna altitude above/below mean sea level (geoid) in meters */
                ,mInfo.sog        /**< ÊýÖµ ¶ÔµØËÙ¶È£¬µ¥Î»Îª½Ú */
                ,mInfo.speed      /**< Speed over the ground in kilometers/hour */
                ,mInfo.direction  /**< Track angle in degrees True */
                ,mInfo.declination /**< Magnetic variation degrees (Easterly var. subtracts from true course) */
                ,mInfo.mode
                ,mInfo.satinfo.inview          // 卫星数量
                ,mInfo.satinfo.inuse          // 卫星数量
                ,mInfo.BDsatinfo.inview          // 卫星数量
                ,mInfo.BDsatinfo.inuse          // 卫星数量
                );       /**< ×Ö·û ¶¨Î»Ä£Ê½±êÖ¾ (A = ×ÔÖ÷Ä£Ê½, D = ²î·ÖÄ£Ê½, E = ¹ÀËãÄ£Ê½, N = Êý¾ÝÎÞÐ§) */
    }


}

void IOMsgHandler::gps_sig_handler(void)
{
    time_t         now = time(NULL);
    static time_t         last = now;
    list<uint32_t> v1;
    uint32_t sat_num = 0;

    if (mLogVerbose[LOG_TYPE_GPS] >= 1) {
        logd("Visible satellites %d", mInfo.satinfo.inview + mInfo.BDsatinfo.inview);
    }
    for (int32_t i = 0; i < mInfo.satinfo.inview; i++) {
        if (mLogVerbose[LOG_TYPE_GPS] >= 1) {
            logd("Satellite #%03d type %-8s SNR %03d(db-HZ)",
                    mInfo.satinfo.sat[i].id, "GPS", (int32_t)mInfo.satinfo.sat[i].sig);
        }

        if (mInfo.satinfo.sat[i].in_use) {
            v1.push_back((uint32_t)mInfo.satinfo.sat[i].sig);
            if (mInfo.satinfo.sat[i].sig > 0)
            {
                sat_num++;
            }
        }
    }
    for (int32_t i = 0; i < mInfo.BDsatinfo.inview; i++) {
        if (mLogVerbose[LOG_TYPE_GPS] >= 1) {
            logd("Satellite #%03d type %-8s SNR %03d(db-HZ)",
                    mInfo.BDsatinfo.sat[i].id, "BEIDOU", (int32_t)mInfo.BDsatinfo.sat[i].sig);
        }

        if (mInfo.BDsatinfo.sat[i].in_use) {
            v1.push_back((uint32_t)mInfo.BDsatinfo.sat[i].sig);
            if (mInfo.BDsatinfo.sat[i].sig > 0)
            {
                sat_num++;
            }
        }
    }
    if (mLogVerbose[LOG_TYPE_GPS] >= 1) {
        logd("Satellite num %d", sat_num);
    }

    if(now < last + 2)  {
        return;
    }
    last = now;

    uint32_t sig_sum = 0;
    v1.sort();
    uint32_t sat_inuse = v1.size();
    uint32_t max_sat_num_cal = sat_inuse > 5 ? 5 : sat_inuse;
    for (uint32_t i = 0; i < max_sat_num_cal; i++) {
        sig_sum += v1.back();
        v1.pop_back();
    }

    mLbs.sat = sat_num; //信号强度大于0的卫星数量

    int avg = sig_sum/max_sat_num_cal;
    if (avg < 20) {
        mLbs.sig_level = 0;
    } else if (avg < 30) {
        mLbs.sig_level = 1;
    } else if (avg < 40) {
        mLbs.sig_level = 2;
    } else {
        mLbs.sig_level = 3;
    }
    //logd("gps avg %d level %d\n", avg, mLbs.sig_level);

}

