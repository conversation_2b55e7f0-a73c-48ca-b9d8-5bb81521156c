#include <sys/system_properties.h>
#include "can.h"
#include "idvr.io.proto.h"

CanCtrl::CanCtrl (io_send_t send, void *context)
    : McuIOHal(send, context)
    , mCanInSilentMode(false)
    , mCanTestMode(false)
    , m<PERSON><PERSON><PERSON><PERSON>(0)
{
    std::string propNames[MCU_CAN_SPEED_MAX] = {
        [MCU_CAN_SPEED_INVALID] = "persist.minieye.CAN_invalid",
        [MCU_CAN_SPEED_1M]      = "persist.minieye.CAN_1M",
        [MCU_CAN_SPEED_800K]    = "persist.minieye.CAN_800k",
        [MCU_CAN_SPEED_500K]    = "persist.minieye.CAN_500k",
        [MCU_CAN_SPEED_250K]    = "persist.minieye.CAN_250k",
        [MCU_CAN_SPEED_125K]    = "persist.minieye.CAN_125k",
        [MCU_CAN_SPEED_100K]    = "persist.minieye.CAN_100k",
        [MCU_CAN_SPEED_50K]     = "persist.minieye.CAN_50k",
        [MCU_CAN_SPEED_20K]     = "persist.minieye.CAN_20k",
        [MCU_CAN_SPEED_10K]     = "persist.minieye.CAN_10k",
        [MCU_CAN_SPEED_5K]      = "persist.minieye.CAN_5k",
    };

    CanBaudParam tmpBuadParam[MCU_CAN_SPEED_MAX] = {
        /*scale   sjw     ts1     ts2*/
        {8,     0x0,    0x6,    0x0},   //CAN_SPEED_INVALID,
        {4,     0x0,    0x6,    0x0},   //CAN_SPEED_1M,
        {5,     0x0,    0x6,    0x0},   //CAN_SPEED_800K,
        {8,     0x0,    0x6,    0x0},   //CAN_SPEED_500K,
        {16,    0x0,    0x6,    0x0},   //CAN_SPEED_250K,
        {32,    0x0,    0x6,    0x0},   //CAN_SPEED_125K,
        {40,    0x0,    0x6,    0x0},   //CAN_SPEED_100K,
        {80,    0x0,    0x6,    0x0},   //CAN_SPEED_50K,
        {200,   0x0,    0x6,    0x0},   //CAN_SPEED_20K,

        /*TODO Not working with CanTest*/
        {400,   0x0,    0x6,    0x0},   //CAN_SPEED_10K,
        /*TODO CanTest not supporting this baud rate*/
        {800,   0x0,    0x6,    0x0},   //CAN_SPEED_5K,
    };
    for (int i = MCU_CAN_SPEED_INVALID; i < MCU_CAN_SPEED_MAX; i++) {
        char propValue[PROP_VALUE_MAX] = {0};
        memset(propValue, 0, sizeof(propValue));
        if (__system_property_get(propNames[i].c_str(), propValue) > 0 ) {
            logd("%s", propValue);
            unsigned int prescaler = 0, sjw = 0, ts1 = 0, ts2 = 0;
            if (4 == sscanf(propValue, "%d,%d,%d,%d", &prescaler, &sjw, &ts1, &ts2)) {
                tmpBuadParam[i].prescaler = (unsigned short)prescaler;
                tmpBuadParam[i].sjw       = (unsigned char)sjw;
                tmpBuadParam[i].ts1       = (unsigned char)ts1;
                tmpBuadParam[i].ts2       = (unsigned char)ts2;
                logd("prescaler %d, sjw %d, ts1 %d, ts2%d", prescaler, sjw, ts1, ts2);
            }
        }
    }
    memcpy(mBaudParam, tmpBuadParam, sizeof(mBaudParam));

    mConfig = new MCUConfig;
}

CanCtrl::~CanCtrl()
{
    delete mConfig;
}
bool CanCtrl::isEnable(CS_TYPE_E type)
{
    return (!mConfig || (mConfig->mConfig.csMap.find(type) != mConfig->mConfig.csMap.end())); 
}
uint8_t CanCtrl::onMessage(uint8_t minor, const char *buf, uint32_t len)
{
    if (len != sizeof(UrtpCanT)) {
        loge("Can Msg len error!\n");
        return 0;
    }

    const UrtpCanT *p = reinterpret_cast<const UrtpCanT *>(buf);

    if(mVerbose > 0) {
        logd("can[%d] ID[0x%x] len[%d]: %02x-%02x-%02x-%02x-%02x-%02x-%02x-%02x\n",\
                minor, p->id, p->len,\
                p->data[0],p->data[1],p->data[2],p->data[3],\
                p->data[4],p->data[5],p->data[6],p->data[7]);
    }

    return 0;
}

void CanCtrl::sendMsg(McuMsgCanIdxE canx, UrtpCanT *pcan)
{
    uint8_t minor = canx == \
                    MCU_CAN_IDX_CAN0_SPEED_RES1 ? CAN_MINOR_SPEED_CAN0_MSG: CAN_MINOR_DISP_CAN1_MSG;
    IOMsg msg(MAJOR_CAN, minor);
    msg.data.append((const char *)pcan, sizeof(UrtpCanT));
    send(msg);
}


void CanCtrl::McuCanConfig(void)
{
    clearAllCanFilter();
    setCanMode(MCU_CAN_IDX_CAN1_DISP_RES1, MCU_CAN_WORKING_MODE_NORMAL, MCU_CAN_SPEED_500K);
    setCanMode(MCU_CAN_IDX_CAN0_SPEED_RES1, MCU_CAN_WORKING_MODE_NORMAL, MCU_CAN_SPEED_250K);

    /*test filter*/
#if 1
    //uint32_t id2[4] = {0x7D1, 0x7F0, 0x7D1, 0x7F0};
    //setCanFilter(MCU_CAN_IDX_CAN0_SPEED, true, false, MCU_CAN_IDX_CAN0_FILTER3, id2, false);

    //uint32_t id3[4] = {0x11111000, 0x11111110, 0x7D1, 0x7F0};
    //setCanFilter(MCU_CAN_IDX_CAN0_SPEED, true, false, MCU_CAN_IDX_CAN0_FILTER4, id3, true);

    //uint32_t speedId[4] = {0x0b2, 0x3db, 0x0b2, 0x3db};
    //setCanFilter(MCU_CAN_IDX_CAN0_SPEED, true, false, MCU_CAN_IDX_CAN0_FILTER4, speedId, false);



    //setCanFilter(MCU_CAN_IDX_CAN1_DISP, true, false, MCU_CAN_IDX_CAN1_DISP_FILTER, id2, false);
#endif
    // 文件/sdcard/run/can_input.json是在setup安装标定的时候写入的
    if (!access(SETUP_CAN_JSON, F_OK)) {
        reloadConfig(SETUP_CAN_JSON);
    } else {
    // 文件/etc/can.json是固件预置的，默认配置的是can速度。
    // 因为某些客户机器不联网，直接装车。所以在出厂的时候配置了一个离线软件包，直接覆盖这个文件，刷成GPS速度
        //mCanTestMode = true;
        reloadConfig(FT_CAN_JSON);
    }
}

void CanCtrl::setCanMode(McuMsgCanIdxE canx, McuCanWorkingModeE mode, McuCanSpeedE baud)
{
    UrtpCanConfigT config;

    config.canx = canx;
    config.prescaler = mBaudParam[baud].prescaler;
    config.sjw = mBaudParam[baud].sjw;
    config.bs1 = mBaudParam[baud].ts1;
    config.bs2 = mBaudParam[baud].ts2;
    config.mode = mode;
    config.flags = CAN_FLAG_SET(CAN_CONFIG_FLAG_ABOM);

    logd("setCanMode can%d pres:%d sjw;%d bs1:%d bs2:%d mode:%d \n",
        config.canx, config.prescaler, config.sjw, config.bs1, config.bs2, config.mode);
    IOMsg msg(MAJOR_CAN, CAN_MINOR_CONFIG);
    msg.data.append((const char *)&config, sizeof(UrtpCanConfigT));
    send(msg);
}

void CanCtrl::clearAllCanFilter(void)
{
    IOMsg msg(MAJOR_CAN, CAN_MINOR_CLEAR_FILTER);
    send(msg);
}

void CanCtrl::clearAllCanFilterAndStart(void)
{
    IOMsg msg(MAJOR_CAN, CAN_MINOR_CLEAR_FILTER);
    send(msg);

    /*Mcu中clear filter没有激活filter, 需要设置一个filter激活*/
    uint32_t speedId[4] = {0x0, 0x0, 0x0, 0x0};
    setCanFilter(MCU_CAN_IDX_CAN0_SPEED_RES1, true, false, MCU_CAN_IDX_CAN0_TSPEED_FILTER, speedId, false);
    setCanFilter(MCU_CAN_IDX_CAN1_DISP_RES1, true, false, MCU_CAN_IDX_CAN1_DISP_FILTER, speedId, false);
}

int32_t CanCtrl::reloadConfig(const char *path)
{
    int32_t ret = 0;
 
    logd("load file %s\n", path);

    ret = mConfig->loadFromFile(path);
    if (0 != ret) {
        loge("Invalid config file");
        return -1;
    }

    mCanInSilentMode = mConfig->mConfig.enable_silent_mode;
    setCanMode(MCU_CAN_IDX_CAN0_SPEED_RES1,
            mCanInSilentMode ? MCU_CAN_WORKING_MODE_SILENT : MCU_CAN_WORKING_MODE_NORMAL,
            mConfig->mConfig.input_can_speed);

    std::map<int32_t, can_signal> listMode;
    std::map<int32_t, can_signal> maskMode;
    for (auto & r : mConfig->mConfig.csMap) {
        if ((r.second.mask > 0) &&
            (r.second.mask < MCU_CAN_EXT_ID_MAX)){
            int32_t mid = r.second.mask & r.second.id;
            maskMode[mid] = r.second;
        } else {
            listMode[r.second.id] = r.second;
        }
    }
    for (auto & r : maskMode) {
        for (auto it = listMode.begin(); it != listMode.end(); it++) {
            if ((r.second.mask & r.second.id) == (r.second.mask & it->second.id)) {
                it = listMode.erase(it);
            }
        }
    }
    int32_t count = 0;
    int32_t idx = 0;
    can_signal tmp[2];
    memset(tmp, 0, sizeof(tmp));
    for (auto & r : listMode) {
        count++;
        if (!(count % 2)) {
            tmp[1] = r.second;
            setCanFilter(tmp[0], tmp[1], (FilterNum_E)idx, true);
            idx++;
            if (idx >= MCU_CAN_IDX_CAN0_FILTER_RES1) {
                break;
            }
        } else {
            tmp[0] = r.second;
        }
    }
    if (idx < MCU_CAN_IDX_CAN0_FILTER_RES1) {
        if ((listMode.size() % 2) == 1) {
            setCanFilter(tmp[0], tmp[1], (FilterNum_E)idx, true);
            idx++;
        }
    }
    if (idx < MCU_CAN_IDX_CAN0_FILTER_RES1) {
        for (auto & r : maskMode) {
            setCanFilter(r.second, r.second, (FilterNum_E)idx, true);
            idx++;
            if (idx >= MCU_CAN_IDX_CAN0_FILTER_RES1) {
                break;
            }
        }
    }

    return 0;
}

/* 
    使用32bit mode
    activate:是否使能过滤器；true:使能    
    majorSig 的 mask屏蔽码有效则判定使用掩码过滤模式；掩码过滤模式只过滤majorSig。
    列表过滤模式可过滤majorSig、minorSig两个can信号。
*/
void CanCtrl::setCanFilter(const can_signal &majorSig, const can_signal &minorSig, FilterNum_E n, bool activate)
{
    uint32_t id[4];
    if (majorSig.mask > 0 && majorSig.mask < MCU_CAN_EXT_ID_MAX) {
        /* 掩码过滤模式 */
        id[0] = majorSig.id;
        id[1] = majorSig.mask;
        setCanFilter(MCU_CAN_IDX_CAN0_SPEED_RES1, activate, false, n, id, true);
    } else {
        /* 列表过滤模式 */
        id[0] = majorSig.id;
        id[1] = minorSig.id;
        setCanFilter(MCU_CAN_IDX_CAN0_SPEED_RES1, activate, true, n, id, true);
    }
}

/*
    stm32的28组过滤器组是can1和can2共享的
    mcu代码已经指定的can2的起始过滤器组是14
    参数:
    activate: 过滤器组是否生效
    bListMode: 列表模式或者掩码模式
    FilterNum_E: 指定过滤器组编号
    id: 过滤id数组
    idNumIsTwo: 如果是32bit mode,就设为1
*/
void CanCtrl::setCanFilter(McuMsgCanIdxE canIdx, bool activate, bool bListMode, FilterNum_E n, uint32_t id[4], bool idNumIsTwo)
{
    UrtpCanFilterT filter;

    if((canIdx == MCU_CAN_IDX_CAN0_SPEED_RES1 && n >= STM32105_CAN1_FIDX_START) ||\
            (canIdx == MCU_CAN_IDX_CAN1_DISP_RES1 && n < STM32105_CAN1_FIDX_START)) {
        loge("filterNum invalid!\n");
        return;
    }

    mFilters[n].enable = activate;
    mFilters[n].bListMode = bListMode;
    memcpy(&mFilters[n].canIds[0], &id[0], sizeof(mFilters[n].canIds));
    //logd("id[%d] size %d - %d\n",n, sizeof(mFilters[n].canIds), sizeof(id));
    if (bListMode) {
        if(idNumIsTwo) {
            bool Id1IsStd = (id[0] < MCU_CAN_STD_ID_MAX) ? true : false;
            bool Id2IsStd = (id[1] < MCU_CAN_STD_ID_MAX) ? true : false;

            mFilters[n].FxR1.u.rm32.IDE = Id1IsStd ? 0 : 1;
            mFilters[n].FxR1.u.rm32.RTR = 0;
            mFilters[n].FxR1.u.rm32.extId = Id1IsStd ? (id[0] << STD_ID_SHIFT) : id[0];

            mFilters[n].FxR2.u.rm32.IDE = Id2IsStd ? 0 : 1;
            mFilters[n].FxR2.u.rm32.RTR = 0;
            mFilters[n].FxR2.u.rm32.extId = Id2IsStd ? (id[1] << STD_ID_SHIFT) : id[1];
            mFilters[n].bBitMode32 = true;

            logd("Id1IsStd %x Id2IsStd %x %x", id[0], id[1], mFilters[n].FxR1.u.rm32.extId);

        } else { /*4 id*/
            if (id[0] >= MCU_CAN_STD_ID_MAX\
                    || id[1] >= MCU_CAN_STD_ID_MAX\
                    || id[2] >= MCU_CAN_STD_ID_MAX\
                    || id[3] >= MCU_CAN_STD_ID_MAX) {
                loge("list Mode: 4 id should be stdid\n");
                return;
            }
            mFilters[n].FxR1.u.rm16[0].stdId = id[0];
            mFilters[n].FxR1.u.rm16[1].stdId = id[1];
            mFilters[n].FxR2.u.rm16[0].stdId = id[2];
            mFilters[n].FxR2.u.rm16[1].stdId = id[3];
            mFilters[n].bBitMode32 = false;
        }
    } else {
        if(idNumIsTwo) {
            bool Id1IsStd = (id[0] < MCU_CAN_STD_ID_MAX) ? true : false;
            bool Id2IsStd = (id[1] < MCU_CAN_STD_ID_MAX) ? true : false;
            if(Id1IsStd || Id2IsStd) {
                logd("mask Mode: 2 id only support ext id!\n");
                return;
            }

            mFilters[n].FxR1.u.rm32.IDE = Id1IsStd ? 0 : 1;
            mFilters[n].FxR1.u.rm32.RTR = 0;
            mFilters[n].FxR1.u.rm32.extId = Id1IsStd ? (id[0] << STD_ID_SHIFT) : id[0];

            mFilters[n].FxR2.u.rm32.IDE = Id2IsStd ? 0 : 1;
            mFilters[n].FxR2.u.rm32.RTR = 0;
            mFilters[n].FxR2.u.rm32.extId = Id2IsStd ? (id[1] << STD_ID_SHIFT) : id[1];
            mFilters[n].bBitMode32 = true;
        } else {
            if (id[0] >= MCU_CAN_STD_ID_MAX\
                    || id[1] >= MCU_CAN_STD_ID_MAX\
                    || id[2] >= MCU_CAN_STD_ID_MAX\
                    || id[3] >= MCU_CAN_STD_ID_MAX) {
                logd("mask Mode: 4 id only support std id!\n");
                return;
            }
            mFilters[n].FxR1.u.rm16[0].stdId = id[0]; //id low
            mFilters[n].FxR1.u.rm16[1].stdId = id[2]; //id high
            mFilters[n].FxR2.u.rm16[0].stdId = id[1]; //mask low
            mFilters[n].FxR2.u.rm16[1].stdId = id[3]; //mask high
            mFilters[n].bBitMode32 = false;
        }
    }

    filter.activate = activate;
    filter.useListMode = bListMode;
    filter.use32BitMode = mFilters[n].bBitMode32;
    filter.filterNumber = n;
    filter.fxR1 = mFilters[n].FxR1.u.raw;
    filter.fxR2 = mFilters[n].FxR2.u.raw;
    logd("filter: %s", mFilters[n].toStr(n));

    IOMsg msg(MAJOR_CAN, CAN_MINOR_SET_FILTER);
    msg.data.append((const char *)&filter, sizeof(UrtpCanFilterT));
    send(msg);
}

void CanCtrl::sendSpeedToDisp(int speed)
{
    int h,z,m,n;
    int speed_color = COLOR_WHITE;

    h = speed / 100;
    z = speed % 100;
    m = z/10;
    n = z%10;

    if (h > 0) {
        speed_color = COLOR_RED;
    }

    logd("send raw %d speed %d %d", speed, m, n);
    mDisp.WarnCar(COLOR_RED, STATE_NOT_SHOW, m, n, STATE_NOT_SHOW, STATE_SHOW, speed_color);
    sendMsg(MCU_CAN_IDX_CAN1_DISP_RES1, &mDisp.mPkg);
}

void CanCtrl::adas_alert_disp(ADAS_CAN700 *pAdasMsg, int speed)
{
    int digit_show = STATE_SHOW;
    int car_show = STATE_SHOW;
    int car_color = COLOR_WHITE;
    int32_t state = STATE_NOT_SHOW;

    static int s_lastspeed = 0xFF;
    static ADAS_CAN700 s_lastAdasMsg;

    float time = pAdasMsg->headway_measurement * 0.1;
    int hw0 = pAdasMsg->headway_measurement/10;
    int hw1 = pAdasMsg->headway_measurement%10;

    if (pAdasMsg->left_ldw != s_lastAdasMsg.left_ldw) {
        logd("left trigger %d ...", pAdasMsg->left_ldw);
        mDisp.displine(pAdasMsg->left_ldw, 0);
        sendMsg(MCU_CAN_IDX_CAN1_DISP_RES1, &mDisp.mPkg);
    }
    if (pAdasMsg->right_ldw != s_lastAdasMsg.right_ldw) {
        mDisp.displine(0, pAdasMsg->right_ldw);
        sendMsg(MCU_CAN_IDX_CAN1_DISP_RES1, &mDisp.mPkg);
        logd("right trigger %d ...", pAdasMsg->right_ldw);
    }

    if (pAdasMsg->peds_in_dz != s_lastAdasMsg.peds_in_dz) {
        logd("peds trigger...");
        state = pAdasMsg->peds_in_dz ? STATE_SHOW : STATE_NOT_SHOW;
        mDisp.WarnPedestrian(state);
        sendMsg(MCU_CAN_IDX_CAN1_DISP_RES1, &mDisp.mPkg);
    }

    if (pAdasMsg->peds_fcw != s_lastAdasMsg.peds_fcw) {
        logd("peds_fcw trigger...");
        state = pAdasMsg->peds_fcw ? STATE_BLINK : STATE_NOT_SHOW;
        mDisp.WarnPedestrian(state);
        sendMsg(MCU_CAN_IDX_CAN1_DISP_RES1, &mDisp.mPkg);
    }

    if (pAdasMsg->fcw_on && !s_lastAdasMsg.fcw_on) {
        logd("fcw trigger...");
        mDisp.WarnCar(COLOR_RED, STATE_BLINK, hw0, hw1, STATE_SHOW, STATE_NOT_SHOW, COLOR_RED);
        sendMsg(MCU_CAN_IDX_CAN1_DISP_RES1, &mDisp.mPkg);
    }
    if (pAdasMsg->fcw_on) { //highest priority
        goto out;
    }

    //WarnLumi(1);
    if (time == 0) {
        digit_show = STATE_NOT_SHOW;
    } else {
        digit_show = STATE_SHOW;
    }
    if (speed <= 0) {
        hw0 = hw1 = 10;
        digit_show = STATE_SHOW;
    }
    if (pAdasMsg->headway_valid) {
        car_show = STATE_SHOW;
    } else {
        car_show = STATE_NOT_SHOW;
    }

    if(pAdasMsg->headway_warning_level == 2) {
        car_color = COLOR_RED;
    } else {
        car_color = COLOR_WHITE;
    }

    if (pAdasMsg->headway_warning_level != s_lastAdasMsg.headway_warning_level ||\
            pAdasMsg->headway_measurement != s_lastAdasMsg.headway_measurement ||\
            s_lastspeed != speed ||\
            pAdasMsg->headway_valid != s_lastAdasMsg.headway_valid) {
        mDisp.WarnCar(car_color, car_show, hw0, hw1, STATE_SHOW, digit_show, COLOR_WHITE);
        sendMsg(MCU_CAN_IDX_CAN1_DISP_RES1, &mDisp.mPkg);
    }

out:
    s_lastspeed = speed;
    s_lastAdasMsg = *pAdasMsg;

    return;
}

